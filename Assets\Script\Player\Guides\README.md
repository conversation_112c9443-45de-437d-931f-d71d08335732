# Th<PERSON> Dẫn Player System

## Tổng Quan
Thư mục này chứa tất cả hướng dẫn chi tiết về Player System, đặc biệt là hệ thống Camera đã được cập nhật.

## Danh Sách Hướng Dẫn

### 📹 Camera System
- **[HUONG_DAN_CAMERA_SYSTEM.md](HUONG_DAN_CAMERA_SYSTEM.md)**: Tổng quan về hệ thống camera
- **[HUONG_DAN_SU_DUNG_CAMERA.md](HUONG_DAN_SU_DUNG_CAMERA.md)**: Hướng dẫn sử dụng chi tiết
- **[TONG_HOP_CAP_NHAT_CAMERA.md](TONG_HOP_CAP_NHAT_CAMERA.md)**: Tổng hợp tất cả thay đổi

### 🎮 Player System (Có sẵn)
- **[HUONG_DAN_TIENG_VIET.md](../HUONG_DAN_TIENG_VIET.md)**: Hướng dẫn tổng quát
- **[HUONG_DAN_CHI_TIET.md](../HUONG_DAN_CHI_TIET.md)**: Hướng dẫn chi tiết
- **[HUONG_DAN_CAP_NHAT_INSPECTOR.md](../HUONG_DAN_CAP_NHAT_INSPECTOR.md)**: Cập nhật Inspector
- **[KHAC_PHUC_LOI.md](../KHAC_PHUC_LOI.md)**: Khắc phục lỗi

### 🔧 Setup và Tools
- **[HUONG_DAN_MO_RONG.md](../HUONG_DAN_MO_RONG.md)**: Mở rộng hệ thống
- **[HUONG_DAN_THEM_MODEL.md](../HUONG_DAN_THEM_MODEL.md)**: Thêm model
- **[HUONG_DAN_TUY_CHINH_MODEL.md](../HUONG_DAN_TUY_CHINH_MODEL.md)**: Tùy chỉnh model

## Cập Nhật Mới Nhất - Camera System

### ✅ Đã Hoàn Thành
1. **Sửa lỗi camera rotation**: Camera không còn orbit quanh nhân vật
2. **Toggle camera mode**: Chuyển đổi First/Third Person bằng phím V
3. **Inspector settings**: Tất cả thông số có thể điều chỉnh trong Edit mode
4. **Collision detection**: Third-person camera tránh xuyên tường
5. **Smooth transitions**: Chuyển đổi mượt mà giữa các chế độ

### 🆕 Scripts Mới
- **CameraTestDemo.cs**: Script test camera với UI demo
- **CameraSetupHelper.cs**: Tool setup camera nhanh trong Editor

### 🛠️ Tools Mới
- **Camera Setup Helper**: Menu Tools → Player System → Camera Setup Helper
- **Demo UI**: F1 để toggle, hiển thị thông tin camera real-time
- **Presets**: Default, Fast-Paced, Cinematic, Tactical

## Cách Sử Dụng Nhanh

### 1. Setup Camera System
```
1. Mở Tools → Player System → Camera Setup Helper
2. Chọn PlayerController trong scene
3. Chọn preset phù hợp
4. Click "Apply Preset"
```

### 2. Test Camera
```
1. Thêm CameraTestDemo component
2. Chạy game
3. Sử dụng phím V để toggle camera
4. F1 để hiển thị demo UI
```

### 3. Customize Settings
```
1. Chọn PlayerController trong Inspector
2. Mở Camera Settings section
3. Điều chỉnh các thông số
4. Thay đổi có hiệu lực ngay lập tức
```

## Phím Tắt Quan Trọng

### Trong Game
- **V**: Toggle First/Third Person camera
- **F1**: Toggle demo UI (nếu có CameraTestDemo)
- **T**: Test camera shake
- **+/-**: Tăng/giảm sensitivity

### Trong Editor
- **Tools → Player System → Camera Setup Helper**: Mở tool setup
- **Sync Settings**: Đồng bộ thông số
- **Reset to Defaults**: Về cài đặt gốc

## Troubleshooting Nhanh

### Camera Giật
- Tăng Smooth Time (0.1 → 0.2)
- Kiểm tra framerate

### Toggle Không Hoạt Động
- Kiểm tra Input System có phím V
- Đảm bảo có PlayerCamera component

### Third-Person Xuyên Tường
- Kiểm tra Collision Layers
- Tăng Collision Buffer

### Sensitivity Không Phù Hợp
- Sử dụng Camera Setup Helper
- Chọn preset phù hợp với game type

## Liên Hệ và Hỗ Trợ

Nếu gặp vấn đề:
1. Đọc hướng dẫn trong thư mục này
2. Kiểm tra Console log
3. Sử dụng CameraTestDemo để debug
4. Reset về default settings nếu cần

## Cấu Trúc Files

```
Guides/
├── README.md (file này)
├── HUONG_DAN_CAMERA_SYSTEM.md
├── HUONG_DAN_SU_DUNG_CAMERA.md
└── TONG_HOP_CAP_NHAT_CAMERA.md

Player/
├── PlayerCamera.cs (đã cập nhật)
├── PlayerController.cs (đã cập nhật)
├── CameraTestDemo.cs (mới)
├── CameraSetupHelper.cs (mới)
└── Editor/
    └── PlayerControllerEditor.cs (đã cập nhật)
```

---

**Lưu ý**: Tất cả hướng dẫn được viết bằng tiếng Việt để dễ hiểu và sử dụng.
