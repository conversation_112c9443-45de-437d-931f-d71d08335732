# Hướng Dẫn Chi Tiết Từng Component

## 1. PlayerController.cs - <PERSON><PERSON> Điều <PERSON>ển <PERSON>h

### Mục Đích
<PERSON> là component trung tâm quản lý tất cả các hệ thống con của nhân vật.

### Cách Hoạt Động
- <PERSON>hận input từ Input System
- Phân phối input cho các component con
- Quản lý trạng thái chung của nhân vật
- Kiểm tra ground check

### Thiết Lập Trong Inspector
```
Player Components:
- Player Movement: Tự động gán
- Player Camera: Tự động gán  
- Player Interaction: Tự động gán
- Player Combat: Tự động gán

Camera Settings:
- Player Camera Component: Gán camera của nhân vật
- Camera Root: Transform chứa camera

Ground Check:
- Ground Check: Transform để kiểm tra mặt đất
- Ground Distance: Khoảng cách kiểm tra (0.4f)
- Ground Mask: Layer của mặt đất
```

### Code Ví Dụ
```csharp
// Lấy reference đến PlayerController
PlayerController player = FindFirstObjectByType<PlayerController>();

// Kiểm tra nhân vật có đang trên mặt đất không
if (player.IsGrounded)
{
    Debug.Log("Nhân vật đang trên mặt đất");
}

// Lấy CharacterController
CharacterController controller = player.CharacterController;
```

## 2. PlayerMovement.cs - Hệ Thống Di Chuyển

### Mục Đích
Xử lý tất cả các loại di chuyển của nhân vật.

### Tính Năng
- Di chuyển với tăng/giảm tốc mượt
- Nhảy với physics thực tế
- Chạy nhanh và bò
- Điều khiển trong không khí

### Thiết Lập Trong Inspector
```
Movement Settings:
- Walk Speed: Tốc độ đi bộ (5f)
- Sprint Speed: Tốc độ chạy (8f)
- Crouch Speed: Tốc độ bò (2f)
- Acceleration: Tăng tốc (10f)
- Deceleration: Giảm tốc (10f)

Jump Settings:
- Jump Height: Độ cao nhảy (2f)
- Gravity: Trọng lực (-9.81f)
- Jump Timeout: Thời gian chờ nhảy (0.1f)
- Fall Timeout: Thời gian chờ rơi (0.15f)

Crouch Settings:
- Crouch Height: Chiều cao khi bò (1f)
- Standing Height: Chiều cao đứng (2f)
- Crouch Transition Speed: Tốc độ chuyển đổi (10f)

Air Control:
- Air Control: Điều khiển trong không khí (0.3f)
```

### Code Ví Dụ
```csharp
PlayerMovement movement = player.GetComponent<PlayerMovement>();

// Kiểm tra trạng thái
if (movement.IsMoving)
    Debug.Log("Nhân vật đang di chuyển");

if (movement.IsSprinting)
    Debug.Log("Nhân vật đang chạy nhanh");

if (movement.IsCrouching)
    Debug.Log("Nhân vật đang bò");

// Thêm lực bên ngoài
movement.AddForce(Vector3.up * 10f);

// Lấy tốc độ hiện tại
float speed = movement.GetCurrentSpeed();
```

## 3. PlayerCamera.cs - Hệ Thống Camera

### Mục Đích
Quản lý camera và điều khiển góc nhìn.

### Tính Năng
- Góc nhìn thứ nhất và thứ ba
- Mouse look mượt mà
- Camera shake
- Hỗ trợ gamepad

### Thiết Lập Trong Inspector
```
Camera Settings:
- Mouse Sensitivity: Độ nhạy chuột (100f)
- Gamepad Sensitivity: Độ nhạy tay cầm (150f)
- Invert Y: Đảo trục Y (false)

Camera Limits:
- Top Clamp: Giới hạn trên (90f)
- Bottom Clamp: Giới hạn dưới (-90f)

Camera Smoothing:
- Smooth Camera: Làm mượt camera (true)
- Smooth Time: Thời gian làm mượt (0.1f)

Camera Shake:
- Shake Decay: Tốc độ giảm rung (5f)

Camera Modes:
- First Person Mode: Chế độ góc nhìn thứ nhất (true)
- Third Person Distance: Khoảng cách góc nhìn thứ ba (5f)
- Third Person Offset: Độ lệch góc nhìn thứ ba (0,2,0)
```

### Code Ví Dụ
```csharp
PlayerCamera playerCam = player.GetComponent<PlayerCamera>();

// Chuyển đổi chế độ camera
playerCam.ToggleCameraMode();

// Thêm hiệu ứng rung
playerCam.AddCameraShake(0.5f);

// Thay đổi độ nhạy
playerCam.SetSensitivity(150f);

// Đảo trục Y
playerCam.SetInvertY(true);

// Lấy hướng camera
Vector3 forward = playerCam.GetCameraForward();
Vector3 right = playerCam.GetCameraRight();
```

## 4. PlayerInteraction.cs - Hệ Thống Tương Tác

### Mục Đích
Xử lý tương tác với các vật thể trong thế giới.

### Tính Năng
- Phát hiện vật thể có thể tương tác
- Hiển thị thông báo tương tác
- Hỗ trợ tương tác liên tục
- Theo dõi tiến trình

### Thiết Lập Trong Inspector
```
Interaction Settings:
- Interaction Range: Phạm vi tương tác (3f)
- Interaction Layer Mask: Layer của vật thể tương tác
- Trigger Interaction: Có tương tác với trigger (Collide)

Interaction Detection:
- Interaction Point: Điểm phát tia (camera)
- Use Sphere Cast: Dùng sphere cast (false)
- Sphere Radius: Bán kính sphere (0.5f)

UI Feedback:
- Interaction Prompt Text: Text thông báo ("Press E to interact")
```

### Code Ví Dụ
```csharp
PlayerInteraction interaction = player.GetComponent<PlayerInteraction>();

// Kiểm tra có vật thể tương tác không
if (interaction.HasInteractable)
{
    string prompt = interaction.GetInteractionPrompt();
    Debug.Log(prompt);
}

// Bắt buộc tương tác với vật thể
IInteractable item = someObject.GetComponent<IInteractable>();
interaction.ForceInteract(item);

// Xóa vật thể tương tác hiện tại
interaction.ClearCurrentInteractable();
```

## 5. PlayerCombat.cs - Hệ Thống Chiến Đấu

### Mục Đích
Xử lý tấn công và chiến đấu cận chiến.

### Tính Năng
- Tấn công cận chiến
- Gây sát thương với lực đẩy
- Hiệu ứng tấn công
- Thời gian hồi chiêu

### Thiết Lập Trong Inspector
```
Combat Settings:
- Attack Damage: Sát thương (10f)
- Attack Range: Phạm vi tấn công (2f)
- Attack Cooldown: Thời gian hồi chiêu (0.5f)
- Enemy Layer Mask: Layer của kẻ thù

Attack Detection:
- Attack Point: Điểm tấn công (camera)
- Use Sphere Cast: Dùng sphere cast (true)
- Attack Radius: Bán kính tấn công (1f)
- Trigger Interaction: Tương tác trigger (Ignore)

Combat Effects:
- Attack Force: Lực đẩy (5f)
- Camera Shake Intensity: Cường độ rung camera (0.1f)
- Hit Stop Duration: Thời gian dừng khi trúng (0.1f)

Animation:
- Attack Animation Duration: Thời gian animation (0.3f)
- Attack Curve: Đường cong tấn công
```

### Code Ví Dụ
```csharp
PlayerCombat combat = player.GetComponent<PlayerCombat>();

// Kiểm tra có thể tấn công không
if (combat.CanAttack)
{
    combat.ForceAttack();
}

// Thay đổi sát thương
combat.SetAttackDamage(20f);

// Thay đổi phạm vi
combat.SetAttackRange(3f);

// Hủy tấn công
combat.CancelAttack();

// Lấy tiến trình hồi chiêu
float progress = combat.AttackCooldownProgress;
```

## 6. Tạo Vật Thể Tương Tác

### Cách Tạo Vật Thể Tương Tác Đơn Giản

```csharp
public class CuaSo : InteractableBase
{
    [SerializeField] private bool isOpen = false;
    [SerializeField] private Animator animator;
    
    public override void Interact(PlayerInteraction playerInteraction)
    {
        isOpen = !isOpen;
        
        if (isOpen)
        {
            animator.SetTrigger("Open");
            Debug.Log("Cửa sổ đã mở");
        }
        else
        {
            animator.SetTrigger("Close");
            Debug.Log("Cửa sổ đã đóng");
        }
    }
    
    public override string GetInteractionPrompt()
    {
        return isOpen ? "Đóng cửa sổ" : "Mở cửa sổ";
    }
}
```

### Cách Tạo Kẻ Thù Nhận Sát Thương

```csharp
public class KeSu : MonoBehaviour, IDamageable
{
    [SerializeField] private float maxHealth = 100f;
    private float currentHealth;
    
    void Start()
    {
        currentHealth = maxHealth;
    }
    
    public void TakeDamage(DamageInfo damageInfo)
    {
        currentHealth -= damageInfo.damage;
        
        Debug.Log($"Kẻ thù nhận {damageInfo.damage} sát thương!");
        
        if (currentHealth <= 0)
        {
            Die();
        }
    }
    
    private void Die()
    {
        Debug.Log("Kẻ thù đã chết!");
        Destroy(gameObject);
    }
    
    public bool IsAlive() => currentHealth > 0;
    public float GetHealth() => currentHealth;
    public float GetMaxHealth() => maxHealth;
}
```

## 7. Sử Dụng Events

### Lắng Nghe Events Từ Player

```csharp
public class GameManager : MonoBehaviour
{
    void Start()
    {
        PlayerInteraction interaction = FindFirstObjectByType<PlayerInteraction>();
        
        // Lắng nghe khi tìm thấy vật thể tương tác
        interaction.OnInteractableFound.AddListener(OnPlayerFoundInteractable);
        
        // Lắng nghe khi tương tác
        interaction.OnInteract.AddListener(OnPlayerInteract);
    }
    
    void OnPlayerFoundInteractable(IInteractable interactable)
    {
        Debug.Log("Tìm thấy vật thể có thể tương tác!");
    }
    
    void OnPlayerInteract(IInteractable interactable)
    {
        Debug.Log("Người chơi đã tương tác!");
    }
}
```

## 8. Tips Tối Ưu

### Hiệu Suất
- Sử dụng Layer Mask để giới hạn raycast
- Tắt các component không cần thiết
- Sử dụng Object Pooling cho hiệu ứng

### Debug
- Bật Gizmos trong Scene view để thấy phạm vi
- Sử dụng Console để theo dõi log
- Dùng PlayerControllerDemo để test

### Tùy Chỉnh
- Thay đổi giá trị trong Inspector trước
- Tạo script con kế thừa để mở rộng
- Sử dụng Events để kết nối với hệ thống khác

Chúc bạn sử dụng hệ thống thành công!
