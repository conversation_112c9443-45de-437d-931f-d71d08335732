using UnityEngine;
using UnityEngine.UI;

/// <summary>
/// Demo script để test các tính năng của hệ thống camera
/// Hi<PERSON>n thị UI để điều chỉnh camera settings real-time
/// </summary>
public class CameraTestDemo : MonoBehaviour
{
    [Header("UI References")]
    [SerializeField] private Canvas demoCanvas;
    [SerializeField] private Text statusText;
    [SerializeField] private Slider sensitivitySlider;
    [SerializeField] private Slider distanceSlider;
    [SerializeField] private Button toggleCameraButton;
    [SerializeField] private Button addShakeButton;
    
    [Header("Demo Settings")]
    [SerializeField] private bool showDemoUI = true;
    [SerializeField] private KeyCode toggleUIKey = KeyCode.F1;
    
    private PlayerCamera playerCamera;
    private PlayerController playerController;
    private bool uiVisible = true;
    
    private void Start()
    {
        // Tìm player components
        FindPlayerComponents();
        
        // Setup demo UI
        SetupDemoUI();
        
        // Setup UI visibility
        if (demoCanvas != null)
        {
            demoCanvas.gameObject.SetActive(showDemoUI && uiVisible);
        }
    }
    
    private void Update()
    {
        // Toggle UI visibility
        if (Input.GetKeyDown(toggleUIKey))
        {
            ToggleUI();
        }
        
        // Update status text
        UpdateStatusDisplay();
        
        // Handle demo input
        HandleDemoInput();
    }
    
    private void FindPlayerComponents()
    {
        // Tìm PlayerController trong scene
        playerController = FindFirstObjectByType<PlayerController>();
        
        if (playerController != null)
        {
            playerCamera = playerController.GetComponent<PlayerCamera>();
            Debug.Log("Camera Test Demo: Found player components");
        }
        else
        {
            Debug.LogWarning("Camera Test Demo: No PlayerController found in scene");
        }
    }
    
    private void SetupDemoUI()
    {
        if (!showDemoUI) return;
        
        // Tạo UI nếu chưa có
        if (demoCanvas == null)
        {
            CreateDemoUI();
        }
        
        // Setup UI events
        if (sensitivitySlider != null)
        {
            sensitivitySlider.minValue = 50f;
            sensitivitySlider.maxValue = 300f;
            sensitivitySlider.value = 100f;
            sensitivitySlider.onValueChanged.AddListener(OnSensitivityChanged);
        }
        
        if (distanceSlider != null)
        {
            distanceSlider.minValue = 2f;
            distanceSlider.maxValue = 10f;
            distanceSlider.value = 5f;
            distanceSlider.onValueChanged.AddListener(OnDistanceChanged);
        }
        
        if (toggleCameraButton != null)
        {
            toggleCameraButton.onClick.AddListener(OnToggleCameraClicked);
        }
        
        if (addShakeButton != null)
        {
            addShakeButton.onClick.AddListener(OnAddShakeClicked);
        }
    }
    
    private void CreateDemoUI()
    {
        // Tạo Canvas
        GameObject canvasObj = new GameObject("Camera Demo Canvas");
        demoCanvas = canvasObj.AddComponent<Canvas>();
        demoCanvas.renderMode = RenderMode.ScreenSpaceOverlay;
        demoCanvas.sortingOrder = 100;
        
        // Thêm CanvasScaler
        CanvasScaler scaler = canvasObj.AddComponent<CanvasScaler>();
        scaler.uiScaleMode = CanvasScaler.ScaleMode.ScaleWithScreenSize;
        scaler.referenceResolution = new Vector2(1920, 1080);
        
        // Thêm GraphicRaycaster
        canvasObj.AddComponent<GraphicRaycaster>();
        
        // Tạo status text
        CreateStatusText();
        
        Debug.Log("Camera Test Demo: Created demo UI");
    }
    
    private void CreateStatusText()
    {
        GameObject textObj = new GameObject("Status Text");
        textObj.transform.SetParent(demoCanvas.transform, false);
        
        statusText = textObj.AddComponent<Text>();
        statusText.font = Resources.GetBuiltinResource<Font>("LegacyRuntime.ttf");
        statusText.fontSize = 16;
        statusText.color = Color.white;
        statusText.text = "Camera Demo Ready";
        
        // Position ở góc trên trái
        RectTransform rectTransform = textObj.GetComponent<RectTransform>();
        rectTransform.anchorMin = new Vector2(0, 1);
        rectTransform.anchorMax = new Vector2(0, 1);
        rectTransform.pivot = new Vector2(0, 1);
        rectTransform.anchoredPosition = new Vector2(10, -10);
        rectTransform.sizeDelta = new Vector2(400, 200);
    }
    
    private void UpdateStatusDisplay()
    {
        if (statusText == null || playerCamera == null) return;
        
        string status = $"=== CAMERA TEST DEMO ===\n";
        status += $"Camera Mode: {(playerCamera.IsFirstPerson ? "First Person" : "Third Person")}\n";
        status += $"Sensitivity: {playerCamera.CurrentSensitivity:F0}\n";
        status += $"Pitch: {playerCamera.GetCurrentPitch():F1}°\n";
        status += $"Yaw: {playerCamera.GetCurrentYaw():F1}°\n";
        status += $"\nControls:\n";
        status += $"V - Toggle Camera Mode\n";
        status += $"F1 - Toggle Demo UI\n";
        status += $"Mouse - Look Around\n";
        status += $"WASD - Move\n";
        
        statusText.text = status;
    }
    
    private void HandleDemoInput()
    {
        if (playerCamera == null) return;
        
        // Test camera shake với phím T
        if (Input.GetKeyDown(KeyCode.T))
        {
            playerCamera.AddCameraShake(0.3f);
            Debug.Log("Camera Test Demo: Added camera shake");
        }
        
        // Test sensitivity adjustment với phím +/-
        if (Input.GetKeyDown(KeyCode.Equals) || Input.GetKeyDown(KeyCode.Plus))
        {
            float newSens = Mathf.Min(playerCamera.CurrentSensitivity + 25f, 300f);
            playerCamera.SetSensitivity(newSens);
            Debug.Log($"Camera Test Demo: Increased sensitivity to {newSens}");
        }
        
        if (Input.GetKeyDown(KeyCode.Minus))
        {
            float newSens = Mathf.Max(playerCamera.CurrentSensitivity - 25f, 50f);
            playerCamera.SetSensitivity(newSens);
            Debug.Log($"Camera Test Demo: Decreased sensitivity to {newSens}");
        }
    }
    
    private void ToggleUI()
    {
        uiVisible = !uiVisible;
        if (demoCanvas != null)
        {
            demoCanvas.gameObject.SetActive(showDemoUI && uiVisible);
        }
        Debug.Log($"Camera Test Demo: UI {(uiVisible ? "shown" : "hidden")}");
    }
    
    // UI Event Handlers
    private void OnSensitivityChanged(float value)
    {
        if (playerCamera != null)
        {
            playerCamera.SetSensitivity(value);
        }
    }
    
    private void OnDistanceChanged(float value)
    {
        if (playerCamera != null)
        {
            playerCamera.SetThirdPersonSettings(value, new Vector3(0, 2, 0));
        }
    }
    
    private void OnToggleCameraClicked()
    {
        if (playerCamera != null)
        {
            playerCamera.ToggleCameraMode();
        }
    }
    
    private void OnAddShakeClicked()
    {
        if (playerCamera != null)
        {
            playerCamera.AddCameraShake(0.5f);
        }
    }
    
    private void OnDestroy()
    {
        // Cleanup UI events
        if (sensitivitySlider != null)
            sensitivitySlider.onValueChanged.RemoveAllListeners();
        
        if (distanceSlider != null)
            distanceSlider.onValueChanged.RemoveAllListeners();
        
        if (toggleCameraButton != null)
            toggleCameraButton.onClick.RemoveAllListeners();
        
        if (addShakeButton != null)
            addShakeButton.onClick.RemoveAllListeners();
    }
}
