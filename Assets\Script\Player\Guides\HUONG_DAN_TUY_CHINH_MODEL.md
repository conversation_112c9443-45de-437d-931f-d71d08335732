# Hướng Dẫn Tùy Chỉnh PlayerModelSetup

## <PERSON><PERSON><PERSON> Thông Số Có Thể Điều Chỉnh

### 1. Model Settings (Cài Đặt Model)

```
Player Model: Model prefab của bạn
Use Animation: <PERSON><PERSON> sử dụng animation không
Hide In First Person: Ẩn model ở góc nhìn thứ nhất
```

### 2. Model Position (Vị Trí Model)

```
Model Offset: Điều chỉnh vị trí model
- (0, 0, 0) = Vị trí gốc
- (0, -0.5, 0) = Hạ xuống 0.5 đơn vị
- (0, 1, 0) = Nâng lên 1 đơn vị

Model Rotation: Xoay model
- (0, 0, 0) = Không xoay
- (0, 180, 0) = Xoay 180 độ (quay mặt)
- (0, 90, 0) = Xoay 90 độ

Model Scale: Kích thước model
- (1, 1, 1) = <PERSON><PERSON>ch thước gốc
- (0.5, 0.5, 0.5) = Nhỏ hơn 50%
- (2, 2, 2) = <PERSON>ớn hơn 200%
```

### 3. Animation Settings (Cài Đặt Animation)

```
Model Animator: Animator component của model
Walk Animation Name: Tên animation đi bộ
Run Animation Name: Tên animation chạy
Idle Animation Name: Tên animation đứng yên
Jump Animation Name: Tên animation nhảy
Crouch Animation Name: Tên animation cúi
```

## Các Vấn Đề Thường Gặp Và Cách Sửa

### Vấn đề 1: Model Quá To/Nhỏ

**Triệu chứng:** Model không vừa với CharacterController

**Cách sửa:**
```
Trong Inspector > Model Scale:
- Model quá to: (0.5, 0.5, 0.5) hoặc (0.8, 0.8, 0.8)
- Model quá nhỏ: (1.5, 1.5, 1.5) hoặc (2, 2, 2)
```

### Vấn đề 2: Model Ở Sai Vị Trí

**Triệu chứng:** Model chìm xuống đất hoặc lơ lửng

**Cách sửa:**
```
Trong Inspector > Model Offset:
- Model chìm xuống: (0, 1, 0) hoặc (0, 0.5, 0)
- Model lơ lửng: (0, -0.5, 0) hoặc (0, -1, 0)
- Model lệch trái/phải: (±0.5, 0, 0)
- Model lệch trước/sau: (0, 0, ±0.5)
```

### Vấn đề 3: Model Quay Sai Hướng

**Triệu chứng:** Model quay mặt về hướng sai

**Cách sửa:**
```
Trong Inspector > Model Rotation:
- Quay mặt 180°: (0, 180, 0)
- Quay trái 90°: (0, -90, 0)
- Quay phải 90°: (0, 90, 0)
- Nghiêng: (±45, 0, 0)
```

### Vấn đề 4: Animation Không Hoạt Động

**Triệu chứng:** Model không có animation khi di chuyển

**Cách sửa:**
```
1. Kiểm tra Use Animation = true
2. Đảm bảo model có Animator component
3. Kiểm tra Animator Controller đã được gán
4. Xem tên animation có đúng không
```

## Tùy Chỉnh Nâng Cao

### 1. Thêm Thông Số Mới

Nếu bạn muốn thêm thông số tùy chỉnh, sửa PlayerModelSetup.cs:

```csharp
[Header("Custom Settings")]
[SerializeField] private float customSpeed = 1f;
[SerializeField] private bool enableCustomFeature = false;
[SerializeField] private Color modelTint = Color.white;
```

### 2. Tùy Chỉnh Animation Parameters

Thêm parameters mới cho Animator:

```csharp
// Thêm vào phần khai báo
private readonly int customParam = Animator.StringToHash("CustomParameter");

// Thêm vào UpdateAnimations()
private void UpdateAnimations()
{
    // Code cũ...
    
    // Thêm parameter mới
    modelAnimator.SetFloat(customParam, customValue);
}
```

### 3. Tùy Chỉnh Model Visibility

Thay đổi cách ẩn/hiện model:

```csharp
private void UpdateModelVisibility()
{
    if (modelRenderers == null || playerCamera == null) return;
    
    bool shouldHide = playerCamera.IsFirstPerson;
    
    // Tùy chỉnh: chỉ ẩn body, giữ lại tay
    foreach (Renderer renderer in modelRenderers)
    {
        if (renderer != null)
        {
            // Kiểm tra tên để quyết định ẩn/hiện
            if (renderer.name.Contains("Body") || renderer.name.Contains("Head"))
            {
                renderer.enabled = !shouldHide;
            }
            else
            {
                renderer.enabled = true; // Luôn hiện tay
            }
        }
    }
}
```

## Script Tùy Chỉnh Riêng

Nếu bạn muốn tạo script riêng, tạo file mới:

```csharp
using UnityEngine;

public class MyCustomModelSetup : MonoBehaviour
{
    [Header("My Custom Settings")]
    [SerializeField] private GameObject myModel;
    [SerializeField] private Vector3 myOffset = Vector3.zero;
    [SerializeField] private float myScale = 1f;
    [SerializeField] private bool myCustomFeature = true;
    
    private GameObject instantiatedModel;
    private PlayerController playerController;
    
    void Start()
    {
        playerController = GetComponent<PlayerController>();
        SetupMyModel();
    }
    
    private void SetupMyModel()
    {
        if (myModel == null) return;
        
        // Tạo model
        instantiatedModel = Instantiate(myModel, transform);
        instantiatedModel.transform.localPosition = myOffset;
        instantiatedModel.transform.localScale = Vector3.one * myScale;
        
        // Tùy chỉnh riêng của bạn
        if (myCustomFeature)
        {
            // Thêm logic tùy chỉnh ở đây
        }
    }
    
    void Update()
    {
        // Cập nhật logic tùy chỉnh
        UpdateMyCustomLogic();
    }
    
    private void UpdateMyCustomLogic()
    {
        // Thêm logic cập nhật của bạn ở đây
    }
}
```

## Công Cụ Debug

Để dễ dàng tùy chỉnh, thêm debug info:

```csharp
void OnGUI()
{
    if (!Application.isPlaying) return;
    
    GUILayout.BeginArea(new Rect(10, 200, 300, 200));
    GUILayout.Label("=== MODEL DEBUG ===");
    
    if (instantiatedModel != null)
    {
        GUILayout.Label($"Model Position: {instantiatedModel.transform.localPosition}");
        GUILayout.Label($"Model Rotation: {instantiatedModel.transform.localEulerAngles}");
        GUILayout.Label($"Model Scale: {instantiatedModel.transform.localScale}");
        
        if (GUILayout.Button("Reset Position"))
        {
            instantiatedModel.transform.localPosition = Vector3.zero;
        }
        
        if (GUILayout.Button("Reset Rotation"))
        {
            instantiatedModel.transform.localRotation = Quaternion.identity;
        }
    }
    
    GUILayout.EndArea();
}
```

## Lưu Preset

Để lưu các thiết lập yêu thích:

```csharp
[System.Serializable]
public class ModelPreset
{
    public string presetName;
    public Vector3 offset;
    public Vector3 rotation;
    public Vector3 scale;
    public bool useAnimation;
}

[Header("Presets")]
[SerializeField] private ModelPreset[] presets;
[SerializeField] private int currentPreset = 0;

public void ApplyPreset(int index)
{
    if (index >= 0 && index < presets.Length)
    {
        ModelPreset preset = presets[index];
        modelOffset = preset.offset;
        modelRotation = preset.rotation;
        modelScale = preset.scale;
        useAnimation = preset.useAnimation;
        
        // Áp dụng ngay lập tức
        if (instantiatedModel != null)
        {
            instantiatedModel.transform.localPosition = modelOffset;
            instantiatedModel.transform.localEulerAngles = modelRotation;
            instantiatedModel.transform.localScale = modelScale;
        }
    }
}
```

## Tips Tùy Chỉnh

1. **Backup trước khi sửa:** Luôn copy script gốc trước khi chỉnh sửa
2. **Test từng bước:** Thay đổi từng thông số một để dễ debug
3. **Sử dụng Gizmos:** Vẽ debug info trong Scene view
4. **Lưu preset:** Tạo các preset cho các loại model khác nhau
5. **Comment code:** Ghi chú những gì bạn đã thay đổi

Bạn muốn tùy chỉnh thông số nào cụ thể? Tôi sẽ hướng dẫn chi tiết hơn!
