# Hướng Dẫn Hệ Thống Player Controller

## Tổng Quan

Đây là một hệ thống điều khiển nhân vật hoàn chỉnh cho Unity sử dụng Input System mới. Hệ thống được thiết kế theo mô-đun, d<PERSON> mở rộng và tùy chỉnh.

## Danh Sách Các File

### File Hệ Thống Chính

1. **PlayerController.cs** - Bộ điều khiển chính quản lý tất cả các hệ thống của nhân vật
2. **PlayerMovement.cs** - <PERSON><PERSON> lý di chuyển, nh<PERSON><PERSON>, chạy nhanh và cúi xuống
3. **PlayerCamera.cs** - Quản lý camera góc nhìn thứ nhất/thứ ba và điều khiển chuột
4. **PlayerInteraction.cs** - <PERSON><PERSON> lý tương tác với các vật thể trong thế giới
5. **PlayerCombat.cs** - Quản lý tấn công và cơ chế chiến đấu
6. **InputSystem_Actions.cs** - Wrapper cho Unity Input System
7. **ExampleInteractable.cs** - Ví dụ về vật thể có thể tương tác và nhận sát thương
8. **PlayerSetupHelper.cs** - Công cụ thiết lập nhanh trong Editor
9. **PlayerControllerDemo.cs** - Script demo với giao diện hiển thị khả năng hệ thống

### File Tài Liệu

10. **README.md** - Hướng dẫn tiếng Anh
11. **HUONG_DAN_TIENG_VIET.md** - Hướng dẫn tiếng Việt (file này)

## Hướng Dẫn Thiết Lập

### Cách 1: Thiết Lập Nhanh (Khuyến Nghị)

1. **Sử dụng Menu Unity:**
   - Vào `GameObject > 3D Object > Player Controller`
   - Hệ thống sẽ tự động tạo nhân vật với đầy đủ cấu hình
   - Camera và ground check sẽ được thiết lập tự động

2. **Tạo Scene Thử Nghiệm:**
   - Vào `Tools > Player Controller > Create Example Scene`
   - Sẽ tạo mặt đất và các vật thể để thử nghiệm

3. **Kiểm Tra Thiết Lập:**
   - Vào `Tools > Player Controller > Validate Setup`
   - Kiểm tra xem có thiếu gì không

### Cách 2: Thiết Lập Thủ Công

1. **Tạo GameObject Player:**
   - Tạo GameObject trống, đặt tên "Player"
   - Thêm component CharacterController
   - Thêm component PlayerInput (của Unity)
   - Thêm script PlayerController

2. **Thiết Lập Camera:**
   - Tạo Camera con của Player
   - Đặt vị trí ở độ cao mắt (khoảng Y = 1.6)
   - Gán vào trường "Player Camera Component"

3. **Cấu Hình Input:**
   - Gán InputSystem_Actions asset vào PlayerInput
   - Đặt Behavior thành "Send Messages"

## Điều Khiển

### Bàn Phím & Chuột
- **Di chuyển:** WASD hoặc phím mũi tên
- **Nhìn xung quanh:** Di chuyển chuột
- **Nhảy:** Phím Space
- **Chạy nhanh:** Giữ Left Shift
- **Cúi xuống:** Phím C
- **Tấn công:** Chuột trái
- **Tương tác:** Phím E
- **Chuyển góc camera:** Phím V

### Tay Cầm (Gamepad)
- **Di chuyển:** Cần trái
- **Nhìn xung quanh:** Cần phải
- **Nhảy:** Nút A
- **Chạy nhanh:** Nhấn cần trái
- **Cúi xuống:** Nút B
- **Tấn công:** Nút X
- **Tương tác:** Nút Y
- **Chuyển góc camera:** Nhấn cần phải

## Tính Năng Chi Tiết

### Hệ Thống Di Chuyển
- **Tăng/giảm tốc mượt mà:** Nhân vật không dừng đột ngột
- **Nhiều tốc độ:** Đi bộ, chạy nhanh, bò
- **Điều khiển trong không khí:** Có thể điều khiển khi nhảy
- **Cúi xuống thông minh:** Kiểm tra va chạm khi đứng lên
- **Coyote time:** Có thể nhảy ngay sau khi rời khỏi mặt đất

### Hệ Thống Camera
- **Hỗ trợ chuột và tay cầm:** Độ nhạy có thể điều chỉnh
- **Hai chế độ:** Góc nhìn thứ nhất và thứ ba
- **Hiệu ứng rung camera:** Khi tấn công hoặc nhận sát thương
- **Camera mượt:** Tùy chọn làm mượt chuyển động camera
- **Khóa con trở tự động:** Trong chế độ góc nhìn thứ nhất

### Hệ Thống Tương Tác
- **Phát hiện vật thể:** Sử dụng raycast hoặc spherecast
- **Phản hồi trực quan:** Làm nổi bật vật thể có thể tương tác
- **Hai loại tương tác:** Tức thì và liên tục
- **Thanh tiến trình:** Cho tương tác dài
- **Thông báo tùy chỉnh:** Mỗi vật thể có thể có thông báo riêng

### Hệ Thống Chiến Đấu
- **Tấn công cận chiến:** Hệ thống tấn công bằng tay
- **Gây sát thương:** Với lực đẩy
- **Thời gian hồi chiêu:** Không thể tấn công liên tục
- **Hiệu ứng tấn công:** Rung camera, dừng thời gian ngắn
- **Giao diện mở rộng:** Dễ dàng thêm loại sát thương mới

## Tùy Chỉnh Hệ Thống

### Thêm Vật Thể Tương Tác Mới

1. **Tạo class kế thừa InteractableBase:**
```csharp
public class CuaRa : InteractableBase
{
    public override void Interact(PlayerInteraction playerInteraction)
    {
        // Logic mở cửa ở đây
        Debug.Log("Cửa đã mở!");
    }
}
```

2. **Gán script vào GameObject và cấu hình trong Inspector**

### Thêm Loại Sát Thương Mới

1. **Implement interface IDamageable:**
```csharp
public class KeSu : MonoBehaviour, IDamageable
{
    public void TakeDamage(DamageInfo damageInfo)
    {
        // Xử lý nhận sát thương
        Debug.Log($"Kẻ thù nhận {damageInfo.damage} sát thương!");
    }
}
```

### Chỉnh Sửa Chuyển Động

1. **Điều chỉnh giá trị trong PlayerMovement component**
2. **Tạo script mới kế thừa PlayerMovement để thêm tính năng**

## Script Demo

### PlayerControllerDemo.cs

Script này hiển thị cách tương tác với hệ thống Player Controller:

**Điều khiển Demo:**
- **Tab:** Bật/tắt giao diện
- **H:** Hồi máu
- **J:** Gây sát thương
- **V:** Chuyển chế độ camera

**Thông tin hiển thị:**
- Trạng thái nhân vật (đang di chuyển, chạy, cúi...)
- Thanh máu
- Thông báo tương tác
- Hướng dẫn điều khiển

## Xử Lý Sự Cố

### Lỗi Thường Gặp

1. **Nhân vật không di chuyển:**
   - Kiểm tra CharacterController đã cấu hình đúng
   - Kiểm tra Ground Layer Mask
   - Đảm bảo Input Actions được gán

2. **Camera không hoạt động:**
   - Kiểm tra camera reference đã được gán
   - Kiểm tra Input System đã thiết lập đúng
   - Đảm bảo con trỏ chuột được khóa

3. **Tương tác không hoạt động:**
   - Kiểm tra Interaction Layer Mask
   - Kiểm tra khoảng cách tương tác
   - Đảm bảo vật thể có script tương tác

4. **Input không phản hồi:**
   - Kiểm tra Input Actions asset đã được gán
   - Đảm bảo Input Actions được enable
   - Kiểm tra binding của các phím

### Tính Năng Debug

- **Hiển thị Ground Check:** Trong Scene view
- **Gizmos tương tác:** Hiển thị phạm vi tương tác
- **Gizmos tấn công:** Hiển thị phạm vi tấn công
- **Console logging:** Ghi log các tương tác và sát thương

## Ý Tưởng Mở Rộng

### Tính Năng Có Thể Thêm

1. **Hệ thống kho đồ:** Tích hợp với tương tác
2. **Chuyển đổi vũ khí:** Nhiều loại vũ khí khác nhau
3. **Hệ thống animation:** Tích hợp với Animator
4. **Giao diện máu/thể lực:** UI bars
5. **Hiệu ứng âm thanh:** Tích hợp audio
6. **Lưu/tải game:** Lưu vị trí và trạng thái

### Tối Ưu Hiệu Suất

- **Object pooling:** Cho hiệu ứng tạm thời
- **Layer mask:** Tối ưu raycast
- **Input processing:** Xử lý input hiệu quả
- **Component modularity:** Tắt các tính năng không dùng

## Yêu Cầu Hệ Thống

- **Unity:** 2022.3 LTS trở lên
- **Input System package:** Đã cài đặt
- **Universal Render Pipeline:** Tùy chọn, cho đồ họa đẹp hơn

## Hỗ Trợ

Nếu gặp vấn đề, hãy:
1. Kiểm tra Console để xem lỗi
2. Sử dụng `Tools > Player Controller > Validate Setup`
3. Xem file README.md để biết thêm chi tiết
4. Kiểm tra các Gizmos trong Scene view

## Kết Luận

Hệ thống Player Controller này cung cấp nền tảng vững chắc cho game 3D. Với thiết kế modular, bạn có thể dễ dàng tùy chỉnh và mở rộng theo nhu cầu của dự án.

Chúc bạn phát triển game thành công!
