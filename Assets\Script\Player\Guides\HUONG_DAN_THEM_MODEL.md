# Hướng Dẫn Thêm Model Vào Player

## Khắc <PERSON>ụ<PERSON> Lỗi Thường Gặp

### Lỗi 1: MissingMethodException: Method 'PlayerController.OnLook' not found

**Nguyên nhân:** PlayerInput component đang sử dụng "Send Messages" behavior nhưng không tìm thấy các method tương ứng.

**Cách khắc phục:** ✅ **Đã được sửa trong PlayerController.cs** - Các method `OnMove`, `OnLook`, `OnJump`, etc. đã được thêm vào.

### Lỗi 2: CS1529: A using clause must precede all other elements

**Nguyên nhân:** Lỗi cú pháp trong PlayerModelSetup.cs với #if UNITY_EDITOR.

**Cách khắc phục:** ✅ **Đã được sửa** - Editor script đã được tách ra file riêng `Editor/PlayerModelSetupEditor.cs`.

### Kiểm tra thiết lập PlayerInput
1. Chọn Player GameObject
2. Trong PlayerInput component:
   - **Actions:** Gán InputSystem_Actions asset
   - **Default Map:** Player
   - **Behavior:** Send Messages
   - **Camera:** Gán camera của player (nếu có)

## Cách Thêm Model Vào Player

### Bước 1: Chuẩn Bị Model

#### Model Không Có Animation
```
1. Import model vào Unity
2. Trong Import Settings:
   - Model tab: Scale Factor = 1
   - Rig tab: Animation Type = None
   - Materials tab: Extract Materials (nếu cần)
3. Apply changes
```

#### Model Có Animation
```
1. Import model với animations
2. Trong Import Settings:
   - Model tab: Scale Factor = 1
   - Rig tab: Animation Type = Humanoid (hoặc Generic)
   - Animation tab: Import Animation = true
3. Apply changes
4. Tạo Animator Controller cho model
```

### Bước 2: Thiết Lập Model Trên Player

#### Cách 1: Sử Dụng PlayerModelSetup Script (Khuyến nghị)

1. **Thêm script vào Player:**
   ```
   - Chọn Player GameObject
   - Add Component > Player Model Setup
   ```

2. **Cấu hình trong Inspector:**
   ```
   Model Settings:
   - Player Model: Kéo prefab model vào đây
   - Use Animation: true (nếu có animation)
   - Hide In First Person: true (ẩn model ở góc nhìn thứ nhất)
   
   Model Position:
   - Model Offset: (0, 0, 0) - điều chỉnh vị trí
   - Model Rotation: (0, 0, 0) - điều chỉnh xoay
   - Model Scale: (1, 1, 1) - điều chỉnh kích thước
   
   Animation Settings:
   - Model Animator: Tự động tìm hoặc gán thủ công
   - Tên các animation: Walk, Run, Idle, Jump, Crouch
   ```

3. **Chạy game để test**

#### Cách 2: Thêm Thủ Công

1. **Tạo model con:**
   ```
   - Kéo model prefab vào Player GameObject
   - Đặt tên "PlayerModel"
   - Điều chỉnh position, rotation, scale
   ```

2. **Thiết lập Animator (nếu có animation):**
   ```
   - Tạo Animator Controller
   - Thêm các animation states
   - Thiết lập parameters và transitions
   ```

### Bước 3: Thiết Lập Animation Controller

#### Tạo Animator Controller

1. **Tạo file:**
   ```
   Right-click trong Project > Create > Animator Controller
   Đặt tên "PlayerAnimatorController"
   ```

2. **Thêm Parameters:**
   ```
   - Speed (Float) - Tốc độ di chuyển
   - IsGrounded (Bool) - Có trên mặt đất
   - IsJumping (Bool) - Có đang nhảy  
   - IsCrouching (Bool) - Có đang cúi
   ```

3. **Tạo States:**
   ```
   - Idle State (default)
   - Walk State
   - Run State  
   - Jump State
   - Crouch State
   ```

4. **Thiết lập Transitions:**
   ```
   Idle -> Walk: Speed > 0.1
   Walk -> Idle: Speed < 0.1
   Walk -> Run: Speed > 5
   Run -> Walk: Speed < 5
   Any State -> Jump: IsJumping = true
   Jump -> Idle: IsGrounded = true && IsJumping = false
   Any State -> Crouch: IsCrouching = true
   Crouch -> Idle: IsCrouching = false
   ```

### Bước 4: Ví Dụ Cụ Thể

#### Model Đơn Giản (Capsule với Texture)

```csharp
// Tạo model đơn giản trong code
public void CreateSimpleModel()
{
    GameObject model = GameObject.CreatePrimitive(PrimitiveType.Capsule);
    model.transform.SetParent(transform);
    model.transform.localPosition = Vector3.zero;
    model.transform.localScale = new Vector3(0.8f, 1f, 0.8f);
    
    // Thêm material
    Material mat = new Material(Shader.Find("Standard"));
    mat.color = Color.blue;
    model.GetComponent<Renderer>().material = mat;
    
    // Xóa collider (player đã có CharacterController)
    DestroyImmediate(model.GetComponent<Collider>());
}
```

#### Model Từ Asset Store

1. **Download model từ Asset Store**
2. **Import vào project**
3. **Kéo prefab vào Player Model field**
4. **Điều chỉnh scale và position**

### Bước 5: Xử Lý Vấn Đề Thường Gặp

#### Model Quá To/Nhỏ
```
Điều chỉnh Model Scale trong PlayerModelSetup:
- Quá to: (0.5, 0.5, 0.5)
- Quá nhỏ: (2, 2, 2)
```

#### Model Ở Sai Vị Trí
```
Điều chỉnh Model Offset:
- Model chìm xuống đất: (0, 1, 0)
- Model lơ lửng: (0, -0.5, 0)
```

#### Model Quay Sai Hướng
```
Điều chỉnh Model Rotation:
- Quay 180 độ: (0, 180, 0)
- Quay 90 độ: (0, 90, 0)
```

#### Animation Không Hoạt Động
```
Kiểm tra:
1. Animator Controller đã được gán
2. Parameters đã được tạo đúng tên
3. Transitions đã được thiết lập
4. Animation clips đã được import
```

### Bước 6: Tối Ưu Model

#### Giảm Poly Count
```
1. Trong Import Settings > Model:
   - Mesh Compression: Medium hoặc High
   - Read/Write Enabled: false
   - Optimize Mesh: true
```

#### Tối Ưu Texture
```
1. Chọn texture files
2. Trong Import Settings:
   - Max Size: 512 hoặc 1024
   - Compression: Compressed
   - Generate Mip Maps: true
```

#### LOD (Level of Detail)
```csharp
// Thêm LOD Group cho model phức tạp
LODGroup lodGroup = model.AddComponent<LODGroup>();
LOD[] lods = new LOD[2];

// LOD 0 - Gần (full detail)
lods[0] = new LOD(0.5f, new Renderer[] { highDetailRenderer });

// LOD 1 - Xa (low detail)  
lods[1] = new LOD(0.1f, new Renderer[] { lowDetailRenderer });

lodGroup.SetLODs(lods);
```

### Bước 7: Script Tùy Chỉnh

#### Thay Đổi Model Runtime

```csharp
public class ModelChanger : MonoBehaviour
{
    [SerializeField] private GameObject[] availableModels;
    private PlayerModelSetup modelSetup;
    
    void Start()
    {
        modelSetup = GetComponent<PlayerModelSetup>();
    }
    
    public void ChangeToModel(int index)
    {
        if (index >= 0 && index < availableModels.Length)
        {
            modelSetup.ChangeModel(availableModels[index]);
        }
    }
}
```

#### Tùy Chỉnh Animation

```csharp
public class CustomAnimations : MonoBehaviour
{
    private PlayerModelSetup modelSetup;
    private Animator animator;
    
    void Start()
    {
        modelSetup = GetComponent<PlayerModelSetup>();
        animator = modelSetup.GetModelAnimator();
    }
    
    public void PlayCustomAnimation(string animName)
    {
        if (animator != null)
        {
            animator.Play(animName);
        }
    }
    
    public void TriggerEmote(string emoteName)
    {
        if (animator != null)
        {
            animator.SetTrigger(emoteName);
        }
    }
}
```

## Checklist Hoàn Thành

```
□ Model đã được import đúng cách
□ PlayerModelSetup script đã được thêm vào Player
□ Model prefab đã được gán vào Player Model field
□ Vị trí, xoay, scale đã được điều chỉnh phù hợp
□ Animator Controller đã được tạo (nếu có animation)
□ Parameters đã được thiết lập trong Animator
□ Transitions đã được cấu hình
□ Model hiển thị đúng trong game
□ Animation hoạt động (nếu có)
□ Model ẩn/hiện đúng khi chuyển góc camera
```

## Lưu Ý Quan Trọng

1. **Backup project** trước khi thêm model
2. **Test trong Play mode** để đảm bảo mọi thứ hoạt động
3. **Kiểm tra performance** nếu model có poly count cao
4. **Sử dụng Profiler** để theo dõi hiệu suất
5. **Tạo prefab** cho player setup hoàn chỉnh

Chúc bạn thành công trong việc thêm model vào player! 🎮
