# Hướng Dẫn Mở Rộng Hệ Thống

## Tạo Các Lo<PERSON>i Vật Thể Tương Tác

### 1. <PERSON><PERSON>a Tự Động

```csharp
using UnityEngine;

public class CuaTuDong : InteractableBase
{
    [Header("Cài Đặt Cửa")]
    [SerializeField] private Transform cuaTransform;
    [SerializeField] private Vector3 viTriMo = new Vector3(0, 0, 90);
    [SerializeField] private Vector3 viTriDong = Vector3.zero;
    [SerializeField] private float tocDoMo = 2f;
    [SerializeField] private AudioClip amThanhMoCua;
    
    private bool dangMo = false;
    private bool daMo = false;
    private AudioSource audioSource;
    
    protected override void Start()
    {
        base.Start();
        audioSource = GetComponent<AudioSource>();
        if (audioSource == null)
            audioSource = gameObject.AddComponent<AudioSource>();
    }
    
    public override void Interact(PlayerInteraction playerInteraction)
    {
        if (dangMo) return;
        
        StartCoroutine(MoCua());
        
        if (amThanhMoCua != null)
            audioSource.PlayOneShot(amThanhMoCua);
    }
    
    private System.Collections.IEnumerator MoCua()
    {
        dangMo = true;
        
        Vector3 viTriBatDau = cuaTransform.localEulerAngles;
        Vector3 viTriKetThuc = daMo ? viTriDong : viTriMo;
        
        float thoiGian = 0;
        while (thoiGian < 1f)
        {
            thoiGian += Time.deltaTime * tocDoMo;
            cuaTransform.localEulerAngles = Vector3.Lerp(viTriBatDau, viTriKetThuc, thoiGian);
            yield return null;
        }
        
        daMo = !daMo;
        dangMo = false;
    }
    
    public override string GetInteractionPrompt()
    {
        return daMo ? "Đóng cửa" : "Mở cửa";
    }
    
    public override bool CanInteract(PlayerInteraction playerInteraction)
    {
        return !dangMo && base.CanInteract(playerInteraction);
    }
}
```

### 2. Hòm Kho Báu

```csharp
using UnityEngine;

public class HomKhoBau : InteractableBase
{
    [Header("Cài Đặt Hòm")]
    [SerializeField] private GameObject[] vatPham;
    [SerializeField] private Transform viTriRoi;
    [SerializeField] private Animator animator;
    [SerializeField] private AudioClip amThanhMoHom;
    
    private bool daMo = false;
    
    public override void Interact(PlayerInteraction playerInteraction)
    {
        if (daMo) return;
        
        MoHom();
    }
    
    private void MoHom()
    {
        daMo = true;
        
        // Chạy animation
        if (animator != null)
            animator.SetTrigger("Mo");
        
        // Phát âm thanh
        AudioSource.PlayClipAtPoint(amThanhMoHom, transform.position);
        
        // Rơi vật phẩm
        foreach (GameObject vatPham in vatPham)
        {
            if (vatPham != null)
            {
                Vector3 viTri = viTriRoi.position + Random.insideUnitSphere * 0.5f;
                Instantiate(vatPham, viTri, Random.rotation);
            }
        }
        
        Debug.Log("Bạn đã mở hòm kho báu!");
    }
    
    public override string GetInteractionPrompt()
    {
        return daMo ? "Hòm đã mở" : "Mở hòm kho báu";
    }
    
    public override bool CanInteract(PlayerInteraction playerInteraction)
    {
        return !daMo && base.CanInteract(playerInteraction);
    }
}
```

### 3. Máy Bán Hàng

```csharp
using UnityEngine;

public class MayBanHang : InteractableBase
{
    [Header("Cài Đặt Máy Bán Hàng")]
    [SerializeField] private GameObject sanPham;
    [SerializeField] private int gia = 10;
    [SerializeField] private Transform viTriRaSanPham;
    [SerializeField] private AudioClip amThanhMua;
    
    // Giả sử có hệ thống tiền
    private int tienCuaNguoiChoi = 100;
    
    public override void Interact(PlayerInteraction playerInteraction)
    {
        if (tienCuaNguoiChoi >= gia)
        {
            MuaSanPham();
        }
        else
        {
            Debug.Log("Không đủ tiền!");
        }
    }
    
    private void MuaSanPham()
    {
        tienCuaNguoiChoi -= gia;
        
        // Tạo sản phẩm
        if (sanPham != null)
        {
            Instantiate(sanPham, viTriRaSanPham.position, viTriRaSanPham.rotation);
        }
        
        // Phát âm thanh
        if (amThanhMua != null)
            AudioSource.PlayClipAtPoint(amThanhMua, transform.position);
        
        Debug.Log($"Đã mua sản phẩm! Tiền còn lại: {tienCuaNguoiChoi}");
    }
    
    public override string GetInteractionPrompt()
    {
        if (tienCuaNguoiChoi >= gia)
            return $"Mua ({gia} xu)";
        else
            return $"Không đủ tiền ({gia} xu)";
    }
}
```

## Tạo Các Loại Kẻ Thù

### 1. Kẻ Thù Cơ Bản

```csharp
using UnityEngine;

public class KeSuCoBan : MonoBehaviour, IDamageable
{
    [Header("Thông Số Kẻ Thù")]
    [SerializeField] private float mauToiDa = 100f;
    [SerializeField] private float tocDoDiChuyen = 3f;
    [SerializeField] private float khoangCachPhatHien = 10f;
    [SerializeField] private GameObject hieuUngChet;
    [SerializeField] private AudioClip amThanhChet;
    
    private float mauHienTai;
    private Transform nguoiChoi;
    private bool daChet = false;
    
    void Start()
    {
        mauHienTai = mauToiDa;
        nguoiChoi = FindFirstObjectByType<PlayerController>()?.transform;
    }
    
    void Update()
    {
        if (daChet || nguoiChoi == null) return;
        
        float khoangCach = Vector3.Distance(transform.position, nguoiChoi.position);
        
        if (khoangCach <= khoangCachPhatHien)
        {
            DiChuyenDenNguoiChoi();
        }
    }
    
    private void DiChuyenDenNguoiChoi()
    {
        Vector3 huong = (nguoiChoi.position - transform.position).normalized;
        transform.position += huong * tocDoDiChuyen * Time.deltaTime;
        transform.LookAt(nguoiChoi);
    }
    
    public void TakeDamage(DamageInfo damageInfo)
    {
        if (daChet) return;
        
        mauHienTai -= damageInfo.damage;
        
        Debug.Log($"Kẻ thù nhận {damageInfo.damage} sát thương! Máu còn: {mauHienTai}");
        
        // Đẩy lùi
        if (damageInfo.force > 0)
        {
            Vector3 huongDay = damageInfo.hitDirection * damageInfo.force;
            transform.position += huongDay * Time.deltaTime;
        }
        
        if (mauHienTai <= 0)
        {
            Chet();
        }
    }
    
    private void Chet()
    {
        daChet = true;
        
        // Hiệu ứng chết
        if (hieuUngChet != null)
            Instantiate(hieuUngChet, transform.position, transform.rotation);
        
        // Âm thanh chết
        if (amThanhChet != null)
            AudioSource.PlayClipAtPoint(amThanhChet, transform.position);
        
        Debug.Log("Kẻ thù đã chết!");
        
        Destroy(gameObject, 0.1f);
    }
    
    public bool IsAlive() => !daChet && mauHienTai > 0;
    public float GetHealth() => mauHienTai;
    public float GetMaxHealth() => mauToiDa;
}
```

### 2. Kẻ Thù Bắn Đạn

```csharp
using UnityEngine;

public class KeSuBanDan : MonoBehaviour, IDamageable
{
    [Header("Thông Số Bắn")]
    [SerializeField] private GameObject vienDan;
    [SerializeField] private Transform diemBan;
    [SerializeField] private float thoiGianBan = 2f;
    [SerializeField] private float tocDoDan = 10f;
    
    private float thoiGianBanCuoi;
    private Transform nguoiChoi;
    private bool daChet = false;
    
    void Start()
    {
        nguoiChoi = FindFirstObjectByType<PlayerController>()?.transform;
    }
    
    void Update()
    {
        if (daChet || nguoiChoi == null) return;
        
        // Nhìn về phía người chơi
        transform.LookAt(nguoiChoi);
        
        // Bắn đạn
        if (Time.time >= thoiGianBanCuoi + thoiGianBan)
        {
            BanDan();
            thoiGianBanCuoi = Time.time;
        }
    }
    
    private void BanDan()
    {
        if (vienDan != null && diemBan != null)
        {
            GameObject dan = Instantiate(vienDan, diemBan.position, diemBan.rotation);
            
            Rigidbody rb = dan.GetComponent<Rigidbody>();
            if (rb != null)
            {
                Vector3 huong = (nguoiChoi.position - diemBan.position).normalized;
                rb.velocity = huong * tocDoDan;
            }
            
            // Hủy đạn sau 5 giây
            Destroy(dan, 5f);
        }
    }
    
    public void TakeDamage(DamageInfo damageInfo)
    {
        // Tương tự như KeSuCoBan
        Debug.Log("Kẻ thù bắn đạn bị tấn công!");
    }
    
    public bool IsAlive() => !daChet;
    public float GetHealth() => 100f;
    public float GetMaxHealth() => 100f;
}
```

## Mở Rộng Hệ Thống Player

### 1. Thêm Hệ Thống Thể Lực

```csharp
using UnityEngine;

public class PlayerStamina : MonoBehaviour
{
    [Header("Cài Đặt Thể Lực")]
    [SerializeField] private float theLucToiDa = 100f;
    [SerializeField] private float tocDoTieuHao = 20f; // Khi chạy
    [SerializeField] private float tocDoHoiPhuc = 10f;
    [SerializeField] private float theLucToiThieuDeChay = 10f;
    
    private float theLucHienTai;
    private PlayerMovement playerMovement;
    
    public float TheLucHienTai => theLucHienTai;
    public float TheLucToiDa => theLucToiDa;
    public bool CoTheChay => theLucHienTai >= theLucToiThieuDeChay;
    
    void Start()
    {
        theLucHienTai = theLucToiDa;
        playerMovement = GetComponent<PlayerMovement>();
    }
    
    void Update()
    {
        if (playerMovement.IsSprinting && playerMovement.IsMoving)
        {
            // Tiêu hao thể lực khi chạy
            theLucHienTai -= tocDoTieuHao * Time.deltaTime;
            theLucHienTai = Mathf.Max(0, theLucHienTai);
        }
        else
        {
            // Hồi phục thể lực
            theLucHienTai += tocDoHoiPhuc * Time.deltaTime;
            theLucHienTai = Mathf.Min(theLucToiDa, theLucHienTai);
        }
    }
    
    public void TieuHaoTheLuc(float luong)
    {
        theLucHienTai -= luong;
        theLucHienTai = Mathf.Max(0, theLucHienTai);
    }
    
    public void HoiPhucTheLuc(float luong)
    {
        theLucHienTai += luong;
        theLucHienTai = Mathf.Min(theLucToiDa, theLucHienTai);
    }
}
```

### 2. Thêm Hệ Thống Kho Đồ Đơn Giản

```csharp
using UnityEngine;
using System.Collections.Generic;

[System.Serializable]
public class VatPham
{
    public string ten;
    public Sprite icon;
    public int soLuong;
    public string moTa;
}

public class PlayerInventory : MonoBehaviour
{
    [Header("Cài Đặt Kho Đồ")]
    [SerializeField] private int soSlotToiDa = 20;
    [SerializeField] private List<VatPham> danhSachVatPham = new List<VatPham>();
    
    public List<VatPham> DanhSachVatPham => danhSachVatPham;
    public int SoSlotTrong => soSlotToiDa - danhSachVatPham.Count;
    
    public bool ThemVatPham(VatPham vatPham)
    {
        // Kiểm tra xem đã có vật phẩm này chưa
        VatPham vatPhamTonTai = danhSachVatPham.Find(v => v.ten == vatPham.ten);
        
        if (vatPhamTonTai != null)
        {
            // Tăng số lượng
            vatPhamTonTai.soLuong += vatPham.soLuong;
            return true;
        }
        else if (SoSlotTrong > 0)
        {
            // Thêm vật phẩm mới
            danhSachVatPham.Add(vatPham);
            return true;
        }
        
        return false; // Kho đồ đầy
    }
    
    public bool XoaVatPham(string tenVatPham, int soLuong = 1)
    {
        VatPham vatPham = danhSachVatPham.Find(v => v.ten == tenVatPham);
        
        if (vatPham != null && vatPham.soLuong >= soLuong)
        {
            vatPham.soLuong -= soLuong;
            
            if (vatPham.soLuong <= 0)
            {
                danhSachVatPham.Remove(vatPham);
            }
            
            return true;
        }
        
        return false;
    }
    
    public bool CoVatPham(string tenVatPham, int soLuong = 1)
    {
        VatPham vatPham = danhSachVatPham.Find(v => v.ten == tenVatPham);
        return vatPham != null && vatPham.soLuong >= soLuong;
    }
}
```

## Tạo UI Đơn Giản

### 1. UI Hiển Thị Máu và Thể Lực

```csharp
using UnityEngine;
using UnityEngine.UI;

public class PlayerUI : MonoBehaviour
{
    [Header("UI References")]
    [SerializeField] private Slider sliderMau;
    [SerializeField] private Slider sliderTheLuc;
    [SerializeField] private Text textTuongTac;
    
    private PlayerController player;
    private PlayerStamina stamina;
    private PlayerInteraction interaction;
    
    // Giả sử có hệ thống máu
    private float mauHienTai = 100f;
    private float mauToiDa = 100f;
    
    void Start()
    {
        player = FindFirstObjectByType<PlayerController>();
        if (player != null)
        {
            stamina = player.GetComponent<PlayerStamina>();
            interaction = player.GetComponent<PlayerInteraction>();
        }
    }
    
    void Update()
    {
        CapNhatUI();
    }
    
    private void CapNhatUI()
    {
        // Cập nhật thanh máu
        if (sliderMau != null)
        {
            sliderMau.value = mauHienTai / mauToiDa;
        }
        
        // Cập nhật thanh thể lực
        if (sliderTheLuc != null && stamina != null)
        {
            sliderTheLuc.value = stamina.TheLucHienTai / stamina.TheLucToiDa;
        }
        
        // Cập nhật text tương tác
        if (textTuongTac != null && interaction != null)
        {
            if (interaction.HasInteractable)
            {
                textTuongTac.text = interaction.GetInteractionPrompt();
                textTuongTac.gameObject.SetActive(true);
            }
            else
            {
                textTuongTac.gameObject.SetActive(false);
            }
        }
    }
    
    public void GayTon(float satThuong)
    {
        mauHienTai -= satThuong;
        mauHienTai = Mathf.Max(0, mauHienTai);
        
        if (mauHienTai <= 0)
        {
            Debug.Log("Người chơi đã chết!");
        }
    }
    
    public void HoiMau(float luong)
    {
        mauHienTai += luong;
        mauHienTai = Mathf.Min(mauToiDa, mauHienTai);
    }
}
```

Những ví dụ này cho thấy cách mở rộng hệ thống Player Controller để tạo ra các tính năng phong phú hơn cho game của bạn!
