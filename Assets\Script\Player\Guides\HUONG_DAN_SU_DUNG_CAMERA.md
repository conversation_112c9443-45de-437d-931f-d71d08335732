# Hướng Dẫn Sử Dụng Hệ Thống Camera

## Các Vấn Đề Đã Được Khắc Phục

### 1. Lỗi Camera Rotation (Đã Sửa)
**Vấn đề cũ**: Camera bị xoay vòng tròn quanh nhân vật thay vì xoay như FPS game
**Giải pháp**: 
- First-person camera giờ xoay nhân vật theo trục Y (trái/phải)
- Camera xoay theo trục X (lên/xuống) 
- Hoạt động như FPS game thông thường

### 2. Chức Năng Chuyển Đổi <PERSON> (Mới)
**Tính năng**: Chuyển đổi mượt mà giữa First-Person và Third-Person
**Đi<PERSON><PERSON> khiển**:
- **Phím V**: Toggle camera mode
- **Gamepad Y Button**: Toggle camera mode

### 3. Thông Số Inspector (<PERSON><PERSON><PERSON>)
**Tất cả thông số hiện có thể điều chỉnh trong Edit mode**:
- Mouse/Gamepad sensitivity
- Camera limits và smoothing
- Third-person distance và offset
- Collision detection settings

## Cách Sử Dụng Chi Tiết

### Setup Ban Đầu
1. Tạo PlayerController bằng PlayerSetupHelper
2. Tất cả thông số camera đã được setup sẵn
3. Có thể điều chỉnh ngay trong Inspector

### Điều Chỉnh Thông Số

#### Camera Sensitivity
- **Mouse Sensitivity**: 50-200 (mặc định: 100)
- **Gamepad Sensitivity**: 100-300 (mặc định: 150)
- **Invert Y**: Đảo trục Y nếu cần

#### Camera Limits
- **Top Clamp**: Giới hạn nhìn lên (0-90°)
- **Bottom Clamp**: Giới hạn nhìn xuống (-90-0°)

#### Third-Person Settings
- **Distance**: Khoảng cách camera (3-10m)
- **Offset**: Độ cao camera (Y: 1-3m)
- **Transition Speed**: Tốc độ chuyển đổi (1-10)
- **Collision Layers**: Layer để kiểm tra va chạm
- **Collision Buffer**: Khoảng cách an toàn (0.1-0.5m)

### Testing và Debug

#### Kiểm Tra First-Person
1. Chạy game
2. Di chuyển chuột trái/phải → nhân vật xoay
3. Di chuyển chuột lên/xuống → camera xoay
4. Không có hiện tượng orbit

#### Kiểm Tra Third-Person
1. Nhấn V để chuyển sang third-person
2. Camera đặt phía sau nhân vật
3. Có collision detection với tường
4. Chuyển đổi mượt mà

#### Kiểm Tra Collision
1. Đi gần tường trong third-person mode
2. Camera tự động điều chỉnh khoảng cách
3. Không xuyên qua tường

## Lưu Ý Quan Trọng

### Performance
- Collision detection chỉ hoạt động trong third-person
- Sử dụng LayerMask để tối ưu performance
- Smooth transition có thể tắt nếu cần

### Customization
- Tất cả thông số có thể thay đổi runtime
- Sử dụng SetCameraSettings() methods
- Inspector values được đồng bộ real-time

### Troubleshooting
- Nếu camera giật: Tăng Smooth Time
- Nếu collision không hoạt động: Kiểm tra LayerMask
- Nếu sensitivity quá cao/thấp: Điều chỉnh trong Inspector

## Code Examples

### Thay Đổi Camera Mode Bằng Code
```csharp
// Lấy PlayerCamera component
PlayerCamera playerCamera = GetComponent<PlayerCamera>();

// Chuyển sang first-person
playerCamera.SetCameraMode(true);

// Chuyển sang third-person
playerCamera.SetCameraMode(false);

// Toggle camera mode
playerCamera.ToggleCameraMode();
```

### Điều Chỉnh Settings Runtime
```csharp
// Thay đổi sensitivity
playerCamera.SetSensitivity(150f);

// Thay đổi third-person settings
playerCamera.SetThirdPersonSettings(7f, new Vector3(0, 2.5f, 0));

// Thay đổi camera limits
playerCamera.SetCameraLimits(80f, -80f);
```

### Kiểm Tra Camera State
```csharp
// Kiểm tra camera mode hiện tại
bool isFirstPerson = playerCamera.IsFirstPerson;

// Lấy camera directions
Vector3 forward = playerCamera.GetCameraForward();
Vector3 right = playerCamera.GetCameraRight();

// Lấy rotation values
float pitch = playerCamera.GetCurrentPitch();
float yaw = playerCamera.GetCurrentYaw();
```

## Advanced Features

### Camera Shake
```csharp
// Thêm camera shake khi tấn công
playerCamera.AddCameraShake(0.2f);
```

### Custom Collision Layers
- Tạo layer riêng cho walls: "CameraCollision"
- Set LayerMask chỉ check layer này
- Tối ưu performance collision detection

### Smooth Camera Transition
- Transition Speed: Điều chỉnh tốc độ chuyển đổi
- Collision Buffer: Khoảng cách an toàn từ tường
- Automatic adjustment khi có obstacle
