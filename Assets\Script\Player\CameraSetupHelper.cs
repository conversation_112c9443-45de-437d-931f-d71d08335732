#if UNITY_EDITOR
using UnityEngine;
using UnityEditor;

/// <summary>
/// Helper script để setup camera system nhanh chóng
/// Cung cấp các preset và quick setup options
/// </summary>
public class CameraSetupHelper : EditorWindow
{
    private PlayerController selectedController;
    private Vector2 scrollPosition;
    
    // Preset values
    private enum CameraPreset
    {
        Default,
        FastPaced,
        Cinematic,
        Tactical
    }
    
    private CameraPreset selectedPreset = CameraPreset.Default;
    
    [MenuItem("Tools/Player System/Camera Setup Helper")]
    public static void ShowWindow()
    {
        CameraSetupHelper window = GetWindow<CameraSetupHelper>("Camera Setup Helper");
        window.minSize = new Vector2(400, 600);
        window.Show();
    }
    
    private void OnGUI()
    {
        scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);
        
        EditorGUILayout.LabelField("Camera Setup Helper", EditorStyles.boldLabel);
        EditorGUILayout.Space();
        
        // Player Controller Selection
        DrawControllerSelection();
        
        if (selectedController != null)
        {
            EditorGUILayout.Space();
            DrawPresetSelection();
            
            EditorGUILayout.Space();
            DrawQuickActions();
            
            EditorGUILayout.Space();
            DrawCurrentSettings();
        }
        
        EditorGUILayout.EndScrollView();
    }
    
    private void DrawControllerSelection()
    {
        EditorGUILayout.LabelField("Player Controller", EditorStyles.miniBoldLabel);
        
        PlayerController newController = (PlayerController)EditorGUILayout.ObjectField(
            "Target Controller", selectedController, typeof(PlayerController), true);
        
        if (newController != selectedController)
        {
            selectedController = newController;
        }
        
        if (selectedController == null)
        {
            EditorGUILayout.HelpBox("Chọn PlayerController để setup camera system", MessageType.Info);
            
            if (GUILayout.Button("Find PlayerController in Scene"))
            {
                selectedController = FindFirstObjectByType<PlayerController>();
                if (selectedController == null)
                {
                    EditorUtility.DisplayDialog("Not Found", "Không tìm thấy PlayerController trong scene", "OK");
                }
            }
        }
    }
    
    private void DrawPresetSelection()
    {
        EditorGUILayout.LabelField("Camera Presets", EditorStyles.miniBoldLabel);
        
        selectedPreset = (CameraPreset)EditorGUILayout.EnumPopup("Preset", selectedPreset);
        
        // Hiển thị thông tin preset
        switch (selectedPreset)
        {
            case CameraPreset.Default:
                EditorGUILayout.HelpBox("Cài đặt cân bằng cho hầu hết game", MessageType.Info);
                break;
            case CameraPreset.FastPaced:
                EditorGUILayout.HelpBox("Sensitivity cao cho FPS/Action game", MessageType.Info);
                break;
            case CameraPreset.Cinematic:
                EditorGUILayout.HelpBox("Camera mượt mà cho adventure game", MessageType.Info);
                break;
            case CameraPreset.Tactical:
                EditorGUILayout.HelpBox("Sensitivity thấp cho strategy/tactical game", MessageType.Info);
                break;
        }
        
        if (GUILayout.Button("Apply Preset"))
        {
            ApplyPreset();
        }
    }
    
    private void DrawQuickActions()
    {
        EditorGUILayout.LabelField("Quick Actions", EditorStyles.miniBoldLabel);
        
        EditorGUILayout.BeginHorizontal();
        if (GUILayout.Button("Reset to Default"))
        {
            ResetToDefault();
        }
        
        if (GUILayout.Button("Optimize for FPS"))
        {
            OptimizeForFPS();
        }
        EditorGUILayout.EndHorizontal();
        
        EditorGUILayout.BeginHorizontal();
        if (GUILayout.Button("Setup Third-Person"))
        {
            SetupThirdPerson();
        }
        
        if (GUILayout.Button("Test Camera"))
        {
            TestCamera();
        }
        EditorGUILayout.EndHorizontal();
    }
    
    private void DrawCurrentSettings()
    {
        EditorGUILayout.LabelField("Current Settings", EditorStyles.miniBoldLabel);
        
        PlayerCamera camera = selectedController.GetComponent<PlayerCamera>();
        if (camera != null)
        {
            EditorGUILayout.LabelField($"Mouse Sensitivity: {camera.CurrentSensitivity}");
            EditorGUILayout.LabelField($"Camera Mode: {(camera.IsFirstPerson ? "First Person" : "Third Person")}");
            
            EditorGUILayout.Space();
            EditorGUILayout.LabelField("Camera Status:", EditorStyles.miniBoldLabel);
            EditorGUILayout.LabelField($"Pitch: {camera.GetCurrentPitch():F1}°");
            EditorGUILayout.LabelField($"Yaw: {camera.GetCurrentYaw():F1}°");
        }
        else
        {
            EditorGUILayout.HelpBox("PlayerCamera component not found", MessageType.Warning);
        }
    }
    
    private void ApplyPreset()
    {
        if (selectedController == null) return;
        
        Undo.RecordObject(selectedController, "Apply Camera Preset");
        
        SerializedObject so = new SerializedObject(selectedController);
        
        switch (selectedPreset)
        {
            case CameraPreset.Default:
                ApplyDefaultPreset(so);
                break;
            case CameraPreset.FastPaced:
                ApplyFastPacedPreset(so);
                break;
            case CameraPreset.Cinematic:
                ApplyCinematicPreset(so);
                break;
            case CameraPreset.Tactical:
                ApplyTacticalPreset(so);
                break;
        }
        
        so.ApplyModifiedProperties();
        EditorUtility.SetDirty(selectedController);
        
        Debug.Log($"Applied {selectedPreset} preset to camera");
    }
    
    private void ApplyDefaultPreset(SerializedObject so)
    {
        so.FindProperty("mouseSensitivity").floatValue = 100f;
        so.FindProperty("gamepadSensitivity").floatValue = 150f;
        so.FindProperty("smoothTime").floatValue = 0.1f;
        so.FindProperty("thirdPersonDistance").floatValue = 5f;
    }
    
    private void ApplyFastPacedPreset(SerializedObject so)
    {
        so.FindProperty("mouseSensitivity").floatValue = 150f;
        so.FindProperty("gamepadSensitivity").floatValue = 200f;
        so.FindProperty("smoothTime").floatValue = 0.05f;
        so.FindProperty("thirdPersonDistance").floatValue = 4f;
    }
    
    private void ApplyCinematicPreset(SerializedObject so)
    {
        so.FindProperty("mouseSensitivity").floatValue = 80f;
        so.FindProperty("gamepadSensitivity").floatValue = 120f;
        so.FindProperty("smoothTime").floatValue = 0.2f;
        so.FindProperty("thirdPersonDistance").floatValue = 7f;
    }
    
    private void ApplyTacticalPreset(SerializedObject so)
    {
        so.FindProperty("mouseSensitivity").floatValue = 60f;
        so.FindProperty("gamepadSensitivity").floatValue = 100f;
        so.FindProperty("smoothTime").floatValue = 0.15f;
        so.FindProperty("thirdPersonDistance").floatValue = 6f;
    }
    
    private void ResetToDefault()
    {
        selectedPreset = CameraPreset.Default;
        ApplyPreset();
    }
    
    private void OptimizeForFPS()
    {
        selectedPreset = CameraPreset.FastPaced;
        ApplyPreset();
        
        // Thêm optimizations cho FPS
        SerializedObject so = new SerializedObject(selectedController);
        so.FindProperty("smoothCamera").boolValue = false; // Tắt smooth cho responsive hơn
        so.ApplyModifiedProperties();
    }
    
    private void SetupThirdPerson()
    {
        if (selectedController == null) return;
        
        PlayerCamera camera = selectedController.GetComponent<PlayerCamera>();
        if (camera != null && camera.IsFirstPerson)
        {
            camera.ToggleCameraMode();
            Debug.Log("Switched to Third Person mode");
        }
    }
    
    private void TestCamera()
    {
        if (selectedController == null) return;
        
        // Thêm CameraTestDemo nếu chưa có
        CameraTestDemo demo = selectedController.GetComponent<CameraTestDemo>();
        if (demo == null)
        {
            selectedController.gameObject.AddComponent<CameraTestDemo>();
            Debug.Log("Added CameraTestDemo component for testing");
        }
        
        EditorUtility.DisplayDialog("Camera Test", 
            "CameraTestDemo đã được thêm vào PlayerController.\n\n" +
            "Chạy game và sử dụng:\n" +
            "- V: Toggle camera mode\n" +
            "- F1: Toggle demo UI\n" +
            "- T: Camera shake\n" +
            "- +/-: Adjust sensitivity", "OK");
    }
}
#endif
