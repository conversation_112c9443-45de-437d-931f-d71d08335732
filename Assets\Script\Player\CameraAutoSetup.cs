using UnityEngine;

/// <summary>
/// Auto setup camera system nếu chưa được cấu hình
/// Script này sẽ tự động tạo camera và cameraRoot nếu cần
/// </summary>
[System.Serializable]
public class CameraAutoSetup
{
    /// <summary>
    /// Tự động setup camera cho PlayerController
    /// </summary>
    /// <param name="playerController">PlayerController cần setup</param>
    /// <returns>True nếu setup thành công</returns>
    public static bool AutoSetupCamera(PlayerController playerController)
    {
        if (playerController == null)
        {
            Debug.LogError("CameraAutoSetup: PlayerController is null!");
            return false;
        }

        bool setupPerformed = false;

        // 1. Kiểm tra và tạo Camera nếu cần
        Camera playerCamera = playerController.PlayerCamera;
        if (playerCamera == null)
        {
            playerCamera = SetupPlayerCamera(playerController);
            setupPerformed = true;
        }

        // 2. <PERSON><PERSON><PERSON> tra và tạo CameraRoot nếu cần
        Transform cameraRoot = playerController.CameraRoot;
        if (cameraRoot == null)
        {
            cameraRoot = SetupCameraRoot(playerController, playerCamera);
            setupPerformed = true;
        }

        // 3. Đảm bảo camera là child của cameraRoot
        if (playerCamera != null && cameraRoot != null)
        {
            if (playerCamera.transform.parent != cameraRoot)
            {
                playerCamera.transform.SetParent(cameraRoot);
                playerCamera.transform.localPosition = Vector3.zero;
                playerCamera.transform.localRotation = Quaternion.identity;
                setupPerformed = true;
                Debug.Log("CameraAutoSetup: Moved camera to be child of CameraRoot");
            }
        }

        // 4. Setup camera settings
        if (playerCamera != null)
        {
            SetupCameraSettings(playerCamera);
        }

        if (setupPerformed)
        {
            Debug.Log("CameraAutoSetup: Camera system setup completed successfully!");
        }

        return setupPerformed;
    }

    /// <summary>
    /// Tạo Camera component cho PlayerController
    /// </summary>
    private static Camera SetupPlayerCamera(PlayerController playerController)
    {
        // Tìm camera existing trong children
        Camera existingCamera = playerController.GetComponentInChildren<Camera>();
        if (existingCamera != null)
        {
            Debug.Log("CameraAutoSetup: Found existing camera in children, using it");
            return existingCamera;
        }

        // Tạo camera mới
        GameObject cameraObj = new GameObject("PlayerCamera");
        cameraObj.transform.SetParent(playerController.transform);
        cameraObj.transform.localPosition = new Vector3(0, 1.8f, 0); // Chiều cao mắt
        cameraObj.transform.localRotation = Quaternion.identity;

        Camera camera = cameraObj.AddComponent<Camera>();
        
        // Thêm AudioListener nếu chưa có
        if (FindObjectOfType<AudioListener>() == null)
        {
            cameraObj.AddComponent<AudioListener>();
        }

        Debug.Log("CameraAutoSetup: Created new PlayerCamera");
        return camera;
    }

    /// <summary>
    /// Tạo CameraRoot cho PlayerController
    /// </summary>
    private static Transform SetupCameraRoot(PlayerController playerController, Camera playerCamera)
    {
        GameObject cameraRootObj = new GameObject("CameraRoot");
        cameraRootObj.transform.SetParent(playerController.transform);
        cameraRootObj.transform.localPosition = new Vector3(0, 1.8f, 0); // Chiều cao mắt
        cameraRootObj.transform.localRotation = Quaternion.identity;

        Debug.Log("CameraAutoSetup: Created CameraRoot");
        return cameraRootObj.transform;
    }

    /// <summary>
    /// Setup các thông số camera cơ bản
    /// </summary>
    private static void SetupCameraSettings(Camera camera)
    {
        // Cài đặt camera cơ bản
        camera.fieldOfView = 60f;
        camera.nearClipPlane = 0.1f;
        camera.farClipPlane = 1000f;
        
        // Tag camera là MainCamera nếu chưa có MainCamera
        if (Camera.main == null)
        {
            camera.tag = "MainCamera";
        }

        Debug.Log("CameraAutoSetup: Applied basic camera settings");
    }

    /// <summary>
    /// Kiểm tra xem camera system đã được setup chưa
    /// </summary>
    public static bool IsCameraSystemSetup(PlayerController playerController)
    {
        if (playerController == null) return false;

        Camera playerCamera = playerController.PlayerCamera;
        Transform cameraRoot = playerController.CameraRoot;

        return playerCamera != null && cameraRoot != null;
    }

    /// <summary>
    /// Validate camera setup và hiển thị warnings nếu cần
    /// </summary>
    public static void ValidateCameraSetup(PlayerController playerController)
    {
        if (playerController == null)
        {
            Debug.LogWarning("CameraAutoSetup: PlayerController is null!");
            return;
        }

        // Kiểm tra Camera
        if (playerController.PlayerCamera == null)
        {
            Debug.LogWarning("CameraAutoSetup: PlayerCamera is not assigned! Use AutoSetupCamera() to fix this.");
        }

        // Kiểm tra CameraRoot
        if (playerController.CameraRoot == null)
        {
            Debug.LogWarning("CameraAutoSetup: CameraRoot is not assigned! Use AutoSetupCamera() to fix this.");
        }

        // Kiểm tra PlayerCamera component
        PlayerCamera playerCameraComponent = playerController.GetComponent<PlayerCamera>();
        if (playerCameraComponent == null)
        {
            Debug.LogWarning("CameraAutoSetup: PlayerCamera component is missing! Add it to the PlayerController GameObject.");
        }

        // Kiểm tra hierarchy
        if (playerController.PlayerCamera != null && playerController.CameraRoot != null)
        {
            if (playerController.PlayerCamera.transform.parent != playerController.CameraRoot)
            {
                Debug.LogWarning("CameraAutoSetup: PlayerCamera should be a child of CameraRoot for proper functionality.");
            }
        }
    }

    /// <summary>
    /// Reset camera về vị trí mặc định
    /// </summary>
    public static void ResetCameraPosition(PlayerController playerController)
    {
        if (playerController == null) return;

        Transform cameraRoot = playerController.CameraRoot;
        Camera playerCamera = playerController.PlayerCamera;

        if (cameraRoot != null)
        {
            cameraRoot.localPosition = new Vector3(0, 1.8f, 0);
            cameraRoot.localRotation = Quaternion.identity;
        }

        if (playerCamera != null)
        {
            playerCamera.transform.localPosition = Vector3.zero;
            playerCamera.transform.localRotation = Quaternion.identity;
        }

        Debug.Log("CameraAutoSetup: Reset camera position to default");
    }

    /// <summary>
    /// Tạo camera setup report
    /// </summary>
    public static string GetSetupReport(PlayerController playerController)
    {
        if (playerController == null) return "PlayerController is null";

        string report = "=== Camera Setup Report ===\n";
        
        // PlayerCamera
        Camera playerCamera = playerController.PlayerCamera;
        report += $"PlayerCamera: {(playerCamera != null ? "✓ Found" : "✗ Missing")}\n";
        
        // CameraRoot
        Transform cameraRoot = playerController.CameraRoot;
        report += $"CameraRoot: {(cameraRoot != null ? "✓ Found" : "✗ Missing")}\n";
        
        // PlayerCamera Component
        PlayerCamera cameraComponent = playerController.GetComponent<PlayerCamera>();
        report += $"PlayerCamera Component: {(cameraComponent != null ? "✓ Found" : "✗ Missing")}\n";
        
        // Hierarchy check
        if (playerCamera != null && cameraRoot != null)
        {
            bool correctHierarchy = playerCamera.transform.parent == cameraRoot;
            report += $"Correct Hierarchy: {(correctHierarchy ? "✓ Yes" : "✗ No")}\n";
        }
        
        // Overall status
        bool isSetup = IsCameraSystemSetup(playerController);
        report += $"Overall Status: {(isSetup ? "✓ Ready" : "✗ Needs Setup")}\n";
        
        return report;
    }
}
