using UnityEngine;
using System.Collections;

public class PlayerCamera : MonoBehaviour
{
    [Header("Camera Settings")]
    [SerializeField] private float mouseSensitivity = 100f;
    [SerializeField] private float gamepadSensitivity = 150f;
    [SerializeField] private bool invertY = false;
    
    [Header("Camera Limits")]
    [SerializeField] private float topClamp = 90f;
    [SerializeField] private float bottomClamp = -90f;
    
    [Header("Camera Smoothing")]
    [SerializeField] private bool smoothCamera = true;
    [SerializeField] private float smoothTime = 0.1f;
    
    [Header("Camera Shake")]
    [SerializeField] private float shakeDecay = 5f;
    
    [Header("Camera Modes")]
    [SerializeField] private bool firstPersonMode = true;
    [SerializeField] private float thirdPersonDistance = 5f;
    [SerializeField] private Vector3 thirdPersonOffset = new Vector3(0, 2, 0);
    [SerializeField] private float cameraTransitionSpeed = 5f;
    [SerializeField] private LayerMask collisionLayers = -1;
    [SerializeField] private float collisionBuffer = 0.2f;
    
    // Private variables
    private PlayerController playerController;
    private Camera playerCamera;
    private Transform cameraRoot;
    
    // Rotation variables
    private float cinemachineTargetPitch;
    private float cinemachineTargetYaw;
    private Vector2 currentLookInput;
    private Vector2 lookVelocity;
    
    // Camera shake
    private Vector3 shakeOffset;
    private float shakeIntensity;
    
    // Third person variables
    private Vector3 thirdPersonTargetPosition;
    private Vector3 thirdPersonCurrentPosition;
    private bool isFirstPerson = true;
    private bool isTransitioning = false;
    
    // Properties
    public bool IsFirstPerson => isFirstPerson;
    public float CurrentSensitivity => mouseSensitivity;
    
    private void Awake()
    {
        playerController = GetComponent<PlayerController>();
    }
    
    private void Start()
    {
        // Validate PlayerController
        if (playerController == null)
        {
            Debug.LogError("PlayerCamera: PlayerController not found! Make sure PlayerCamera is attached to the same GameObject as PlayerController.");
            return;
        }

        // Auto setup camera system if needed
        bool setupPerformed = CameraAutoSetup.AutoSetupCamera(playerController);
        if (setupPerformed)
        {
            Debug.Log("PlayerCamera: Auto setup completed.");
        }

        // Get camera references
        playerCamera = playerController.PlayerCamera;
        cameraRoot = playerController.CameraRoot;

        // Final validation
        if (playerCamera == null || cameraRoot == null)
        {
            Debug.LogError("PlayerCamera: Failed to setup camera system! Check the setup report:");
            Debug.LogError(CameraAutoSetup.GetSetupReport(playerController));
            return;
        }

        // Initialize rotation values
        cinemachineTargetYaw = transform.eulerAngles.y;
        cinemachineTargetPitch = cameraRoot.localEulerAngles.x;

        // Initialize camera mode
        isFirstPerson = firstPersonMode;

        // Initialize third person position
        thirdPersonCurrentPosition = playerCamera.transform.position;

        // Lock cursor in first person
        if (isFirstPerson)
        {
            SetCursorState(true);
        }

        Debug.Log("PlayerCamera: Initialized successfully.");
    }
    
    public void UpdateCamera(Vector2 lookInput)
    {
        // Early return if camera is not properly initialized
        if (playerCamera == null || cameraRoot == null)
        {
            return;
        }

        HandleLookInput(lookInput);
        HandleCameraRotation();
        HandleCameraShake();
        HandleCameraMode();
    }
    
    private void HandleLookInput(Vector2 lookInput)
    {
        // Determine sensitivity based on input device
        float sensitivity = mouseSensitivity;
        
        // Check if input is from gamepad (typically has smaller values)
        if (Mathf.Abs(lookInput.x) < 1f && Mathf.Abs(lookInput.y) < 1f && lookInput != Vector2.zero)
        {
            sensitivity = gamepadSensitivity;
        }
        
        // Apply sensitivity and time
        Vector2 scaledInput = lookInput * sensitivity * Time.deltaTime;
        
        // Invert Y if needed
        if (invertY)
        {
            scaledInput.y = -scaledInput.y;
        }
        
        // Smooth input if enabled
        if (smoothCamera)
        {
            currentLookInput = Vector2.SmoothDamp(currentLookInput, scaledInput, ref lookVelocity, smoothTime);
        }
        else
        {
            currentLookInput = scaledInput;
        }
    }
    
    private void HandleCameraRotation()
    {
        // Update target rotations
        cinemachineTargetYaw += currentLookInput.x;
        cinemachineTargetPitch -= currentLookInput.y;

        // Clamp pitch rotation
        cinemachineTargetPitch = Mathf.Clamp(cinemachineTargetPitch, bottomClamp, topClamp);

        // Apply rotations based on camera mode
        if (isFirstPerson)
        {
            // First person: rotate player body for yaw (horizontal look), camera for pitch (vertical look)
            // This creates natural FPS camera movement
            transform.rotation = Quaternion.Euler(0f, cinemachineTargetYaw, 0f);

            if (cameraRoot != null)
            {
                cameraRoot.localRotation = Quaternion.Euler(cinemachineTargetPitch, 0f, 0f);
            }
        }
        else
        {
            // Third person: camera orbits around player
            HandleThirdPersonRotation();
        }
    }
    
    private void HandleThirdPersonRotation()
    {
        if (playerCamera == null) return;

        // Calculate desired camera position
        Vector3 direction = new Vector3(0, 0, -thirdPersonDistance);
        Quaternion rotation = Quaternion.Euler(cinemachineTargetPitch, cinemachineTargetYaw, 0);

        Vector3 targetPosition = transform.position + thirdPersonOffset + rotation * direction;

        // Perform collision detection
        Vector3 finalPosition = HandleThirdPersonCollision(targetPosition);

        // Smooth camera movement
        if (isTransitioning)
        {
            thirdPersonCurrentPosition = Vector3.Lerp(thirdPersonCurrentPosition, finalPosition,
                cameraTransitionSpeed * Time.deltaTime);
        }
        else
        {
            thirdPersonCurrentPosition = finalPosition;
        }

        // Apply position and rotation
        playerCamera.transform.position = thirdPersonCurrentPosition;
        playerCamera.transform.LookAt(transform.position + thirdPersonOffset);

        // Also rotate player body to match camera yaw in third person
        transform.rotation = Quaternion.Euler(0f, cinemachineTargetYaw, 0f);
    }

    private Vector3 HandleThirdPersonCollision(Vector3 targetPosition)
    {
        Vector3 startPos = transform.position + thirdPersonOffset;
        Vector3 directionToCamera = (targetPosition - startPos).normalized;
        float distanceToCamera = Vector3.Distance(startPos, targetPosition);

        RaycastHit hit;
        if (Physics.Raycast(startPos, directionToCamera, out hit, distanceToCamera,
            collisionLayers, QueryTriggerInteraction.Ignore))
        {
            // Position camera slightly in front of the collision point
            return hit.point - directionToCamera * collisionBuffer;
        }

        return targetPosition;
    }
    
    private void HandleCameraShake()
    {
        if (shakeIntensity > 0)
        {
            // Generate random shake offset
            shakeOffset = Random.insideUnitSphere * shakeIntensity;
            shakeOffset.z = 0; // Don't shake forward/backward
            
            // Apply shake to camera
            if (isFirstPerson)
            {
                cameraRoot.localPosition += shakeOffset;
            }
            else
            {
                playerCamera.transform.position += shakeOffset;
            }
            
            // Decay shake
            shakeIntensity = Mathf.Lerp(shakeIntensity, 0, shakeDecay * Time.deltaTime);
        }
    }
    
    private void HandleCameraMode()
    {
        // This can be extended to handle camera mode switching
        // For now, we maintain the current mode
    }
    
    public void ToggleCameraMode()
    {
        isFirstPerson = !isFirstPerson;
        isTransitioning = true;

        if (isFirstPerson)
        {
            // Switch to first person
            SetCursorState(true);

            // Reset camera position to first person smoothly
            if (cameraRoot != null)
            {
                // Store current third person position for smooth transition
                thirdPersonCurrentPosition = playerCamera.transform.position;

                // Reset camera to first person position
                playerCamera.transform.localPosition = Vector3.zero;
                playerCamera.transform.localRotation = Quaternion.identity;
                cameraRoot.localPosition = Vector3.zero;
                cameraRoot.localRotation = Quaternion.Euler(cinemachineTargetPitch, 0f, 0f);
            }
        }
        else
        {
            // Switch to third person
            SetCursorState(false);

            // Initialize third person position
            Vector3 direction = new Vector3(0, 0, -thirdPersonDistance);
            Quaternion rotation = Quaternion.Euler(cinemachineTargetPitch, cinemachineTargetYaw, 0);
            thirdPersonCurrentPosition = transform.position + thirdPersonOffset + rotation * direction;
        }

        // Stop transition after a short delay
        StartCoroutine(StopTransitionAfterDelay());
    }

    private System.Collections.IEnumerator StopTransitionAfterDelay()
    {
        yield return new WaitForSeconds(0.5f);
        isTransitioning = false;
    }
    
    public void AddCameraShake(float intensity)
    {
        shakeIntensity = Mathf.Max(shakeIntensity, intensity);
    }
    
    public void SetSensitivity(float newSensitivity)
    {
        mouseSensitivity = newSensitivity;
    }
    
    public void SetInvertY(bool invert)
    {
        invertY = invert;
    }
    
    private void SetCursorState(bool locked)
    {
        Cursor.lockState = locked ? CursorLockMode.Locked : CursorLockMode.None;
        Cursor.visible = !locked;
    }
    
    // Public methods for external access
    public Vector3 GetCameraForward()
    {
        if (playerCamera == null) return transform.forward;
        return playerCamera.transform.forward;
    }

    public Vector3 GetCameraRight()
    {
        if (playerCamera == null) return transform.right;
        return playerCamera.transform.right;
    }

    public Vector3 GetCameraUp()
    {
        if (playerCamera == null) return transform.up;
        return playerCamera.transform.up;
    }
    
    public float GetCurrentPitch()
    {
        return cinemachineTargetPitch;
    }
    
    public float GetCurrentYaw()
    {
        return cinemachineTargetYaw;
    }
    
    private void OnApplicationFocus(bool hasFocus)
    {
        if (isFirstPerson)
        {
            SetCursorState(hasFocus);
        }
    }

    // Setter methods for PlayerController
    public void SetCameraSettings(float mouseSens, float gamepadSens, bool invert)
    {
        mouseSensitivity = mouseSens;
        gamepadSensitivity = gamepadSens;
        invertY = invert;
    }

    public void SetCameraLimits(float top, float bottom)
    {
        topClamp = top;
        bottomClamp = bottom;
    }

    public void SetSmoothSettings(bool smooth, float time)
    {
        smoothCamera = smooth;
        smoothTime = time;
    }

    public void SetShakeSettings(float decay)
    {
        shakeDecay = decay;
    }

    public void SetThirdPersonSettings(float distance, Vector3 offset)
    {
        thirdPersonDistance = distance;
        thirdPersonOffset = offset;
    }

    public void SetCameraTransitionSettings(float transitionSpeed, LayerMask collisionLayers, float collisionBuffer)
    {
        this.cameraTransitionSpeed = transitionSpeed;
        this.collisionLayers = collisionLayers;
        this.collisionBuffer = collisionBuffer;
    }

    public void SetCameraMode(bool firstPerson)
    {
        if (isFirstPerson != firstPerson)
        {
            ToggleCameraMode();
        }
    }
}
