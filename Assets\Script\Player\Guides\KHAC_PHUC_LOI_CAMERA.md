# Khắc <PERSON>ục Lỗi Camera System

## Lỗi Thường Gặp và Cách Sửa

### 1. NullReferenceException trong PlayerCamera.Start()

#### Nguyên nhân:
- PlayerController không có Camera được gán
- CameraRoot chưa được setup
- PlayerCamera component thiếu

#### C<PERSON>ch sửa:
```csharp
// Tự động sửa bằng code
CameraAutoSetup.AutoSetupCamera(playerController);

// Hoặc manual setup:
1. Chọn PlayerController trong Hierarchy
2. Kéo Camera vào PlayerCamera field
3. Tạo empty GameObject làm CameraRoot
4. Đặt Camera làm child của CameraRoot
```

#### Kiểm tra setup:
```csharp
// Trong Console
CameraAutoSetup.ValidateCameraSetup(playerController);
Debug.Log(CameraAutoSetup.GetSetupReport(playerController));
```

### 2. Camera Không Di Chuyển

#### Nguyên nhân:
- Input System chưa được setup
- PlayerController không gọi UpdateCamera()
- Camera bị lock position

#### C<PERSON><PERSON> sửa:
1. <PERSON>ểm tra Input System có phím Look
2. Đảm bảo PlayerController.Update() gọi UpdateCamera()
3. Kiểm tra Cursor.lockState

### 3. Toggle Camera Không Hoạt Động

#### Nguyên nhân:
- Input System thiếu phím V (ToggleCamera)
- PlayerCamera component thiếu
- Method OnToggleCamera không được gọi

#### Cách sửa:
1. Mở InputSystem_Actions.inputactions
2. Kiểm tra có action "ToggleCamera" với phím V
3. Đảm bảo PlayerController có method OnToggleCamera()

### 4. Third-Person Camera Xuyên Tường

#### Nguyên nhân:
- Collision Layers không đúng
- Collision Buffer quá nhỏ
- Walls không có Collider

#### Cách sửa:
1. Kiểm tra walls có Collider
2. Set Collision Layers = "Default" hoặc "Wall"
3. Tăng Collision Buffer (0.2 → 0.5)

### 5. Camera Giật Lag

#### Nguyên nhân:
- Smooth Time quá cao
- Framerate thấp
- Transition Speed quá cao

#### Cách sửa:
1. Giảm Smooth Time (0.1 → 0.05)
2. Tắt Smooth Camera nếu cần responsive
3. Giảm Transition Speed (5 → 3)

## Quick Fix Commands

### Auto Setup Camera
```csharp
// Trong Console hoặc script
PlayerController pc = Object.FindObjectOfType<PlayerController>();
CameraAutoSetup.AutoSetupCamera(pc);
```

### Validate System
```csharp
// Thêm CameraSystemValidator để test
gameObject.AddComponent<CameraSystemValidator>();
// Hoặc sử dụng phím tắt F2, F3, F4
```

### Reset Camera Position
```csharp
CameraAutoSetup.ResetCameraPosition(playerController);
```

### Validate Setup
```csharp
CameraAutoSetup.ValidateCameraSetup(playerController);
```

### Get Setup Report
```csharp
Debug.Log(CameraAutoSetup.GetSetupReport(playerController));
```

## Debugging Tools

### 1. Camera Test Demo
```csharp
// Thêm vào PlayerController
gameObject.AddComponent<CameraTestDemo>();
```

### 2. Camera Setup Helper
```
Menu: Tools → Player System → Camera Setup Helper
```

### 3. Console Commands
```csharp
// Kiểm tra camera state
PlayerCamera cam = Object.FindObjectOfType<PlayerCamera>();
Debug.Log($"Is First Person: {cam.IsFirstPerson}");
Debug.Log($"Current Pitch: {cam.GetCurrentPitch()}");
Debug.Log($"Current Yaw: {cam.GetCurrentYaw()}");
```

### 4. System Validator
```csharp
// Thêm CameraSystemValidator component
CameraSystemValidator validator = gameObject.AddComponent<CameraSystemValidator>();
// Sử dụng phím F2 để validate, F3 cho report, F4 để auto-fix
```

## Checklist Troubleshooting

### Setup Cơ Bản
- [ ] PlayerController có PlayerCamera component
- [ ] PlayerCamera field được gán Camera
- [ ] CameraRoot được tạo và gán
- [ ] Camera là child của CameraRoot
- [ ] Input System có ToggleCamera action

### Functionality
- [ ] Mouse look hoạt động trong first-person
- [ ] Phím V toggle camera mode
- [ ] Third-person có collision detection
- [ ] Camera không giật khi chuyển đổi
- [ ] Sensitivity settings có hiệu lực

### Performance
- [ ] Framerate ổn định (>30 FPS)
- [ ] Collision detection không lag
- [ ] Smooth camera không quá mượt
- [ ] Memory usage hợp lý

## Lỗi Đặc Biệt

### Camera Bị Đảo Ngược
```csharp
// Sửa trong Inspector
Invert Y = true/false

// Hoặc bằng code
playerCamera.SetInvertY(true);
```

### Sensitivity Quá Cao/Thấp
```csharp
// Điều chỉnh trong Inspector hoặc code
playerCamera.SetSensitivity(100f); // Mouse
playerCamera.SetCameraSettings(100f, 150f, false); // Mouse, Gamepad, InvertY
```

### Camera Stuck ở Third-Person
```csharp
// Force về first-person
playerCamera.SetCameraMode(true);
```

### Input Không Hoạt Động
1. Kiểm tra PlayerInput component
2. Kiểm tra Input Actions asset
3. Kiểm tra Behavior = "Send Messages"
4. Kiểm tra method names đúng (OnLook, OnToggleCamera)

## Liên Hệ Hỗ Trợ

Nếu vẫn gặp lỗi:
1. Copy error message từ Console
2. Chạy GetSetupReport() và copy kết quả
3. Kiểm tra Unity version và Input System version
4. Thử tạo scene mới với PlayerController clean

## Prevention Tips

### Để Tránh Lỗi:
1. Luôn sử dụng CameraAutoSetup.AutoSetupCamera()
2. Kiểm tra null trước khi access camera
3. Sử dụng try-catch cho critical operations
4. Test trên nhiều scenes khác nhau
5. Backup scene trước khi thay đổi lớn

### Best Practices:
1. Setup camera qua Tools menu thay vì manual
2. Sử dụng presets thay vì custom values
3. Test với CameraTestDemo trước khi deploy
4. Đọc Console logs để catch warnings sớm
5. Validate setup sau mỗi lần thay đổi
